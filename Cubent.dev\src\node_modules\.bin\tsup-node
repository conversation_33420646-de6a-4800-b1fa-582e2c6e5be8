#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules/tsup/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules/tsup/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules/tsup/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules/tsup/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/tsup@8.5.0_jiti@2.4.2_postc_f8044a995010cee2c801c1b19141623c/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../tsup/dist/cli-node.js" "$@"
else
  exec node  "$basedir/../tsup/dist/cli-node.js" "$@"
fi
