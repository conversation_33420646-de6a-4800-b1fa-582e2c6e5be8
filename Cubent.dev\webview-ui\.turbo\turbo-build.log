
> @cubent/vscode-webview@ build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui
> tsc -b && vite build

[36mvite v6.3.5 [32mbuilding for production...[36m[39m
transforming...
[33m[plugin vite:resolve] Module "fs/promises" has been externalized for browser compatibility, imported by "C:/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/src/core/prompts/sections/custom-instructions.ts". See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.[39m
[33m[plugin vite:resolve] Module "path" has been externalized for browser compatibility, imported by "C:/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/src/core/prompts/sections/custom-instructions.ts". See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.[39m
[32m✓[39m 5302 modules transformed.
rendering chunks...
[2m../src/webview-ui/build/[22m[32mindex.html                              [39m[1m[2m    0.38 kB[22m[1m[22m
[2m../src/webview-ui/build/[22m[32massets/codicon.ttf                      [39m[1m[2m   80.19 kB[22m[1m[22m
[2m../src/webview-ui/build/[22m[35massets/index.css                        [39m[1m[2m  110.31 kB[22m[1m[22m
[2m../src/webview-ui/build/[22m[36massets/clone.js                         [39m[1m[2m    0.12 kB[22m[1m[22m[2m │ map:     1.42 kB[22m
[2m../src/webview-ui/build/[22m[36massets/channel.js                       [39m[1m[2m    0.13 kB[22m[1m[22m[2m │ map:     0.56 kB[22m
[2m../src/webview-ui/build/[22m[36massets/init.js                          [39m[1m[2m    0.18 kB[22m[1m[22m[2m │ map:     1.07 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-XZIHB7SX.js                [39m[1m[2m    0.22 kB[22m[1m[22m[2m │ map:     0.87 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-4BMEZGHF.js                [39m[1m[2m    0.32 kB[22m[1m[22m[2m │ map:     0.97 kB[22m
[2m../src/webview-ui/build/[22m[36massets/classDiagram-GIVACNV2.js         [39m[1m[2m    0.34 kB[22m[1m[22m[2m │ map:     1.47 kB[22m
[2m../src/webview-ui/build/[22m[36massets/classDiagram-v2-COTLJTTW.js      [39m[1m[2m    0.35 kB[22m[1m[22m[2m │ map:     1.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/stateDiagram-v2-YXO3MK2T.js      [39m[1m[2m    0.35 kB[22m[1m[22m[2m │ map:     1.49 kB[22m
[2m../src/webview-ui/build/[22m[36massets/codeowners.js                    [39m[1m[2m    0.59 kB[22m[1m[22m[2m │ map:     1.11 kB[22m
[2m../src/webview-ui/build/[22m[36massets/infoDiagram-PH2N3AL5.js          [39m[1m[2m    0.66 kB[22m[1m[22m[2m │ map:     2.33 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-RZ5BOZE2.js                [39m[1m[2m    0.73 kB[22m[1m[22m[2m │ map:     3.09 kB[22m
[2m../src/webview-ui/build/[22m[36massets/shellsession.js                  [39m[1m[2m    0.74 kB[22m[1m[22m[2m │ map:     1.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tsv.js                           [39m[1m[2m    0.77 kB[22m[1m[22m[2m │ map:     1.39 kB[22m
[2m../src/webview-ui/build/[22m[36massets/html-derivative.js               [39m[1m[2m    0.92 kB[22m[1m[22m[2m │ map:     1.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/git-rebase.js                    [39m[1m[2m    1.01 kB[22m[1m[22m[2m │ map:     1.69 kB[22m
[2m../src/webview-ui/build/[22m[36massets/qmldir.js                        [39m[1m[2m    1.04 kB[22m[1m[22m[2m │ map:     1.75 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fortran-fixed-form.js            [39m[1m[2m    1.14 kB[22m[1m[22m[2m │ map:     1.96 kB[22m
[2m../src/webview-ui/build/[22m[36massets/csv.js                           [39m[1m[2m    1.18 kB[22m[1m[22m[2m │ map:     2.01 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ordinal.js                       [39m[1m[2m    1.22 kB[22m[1m[22m[2m │ map:     5.15 kB[22m
[2m../src/webview-ui/build/[22m[36massets/git-commit.js                    [39m[1m[2m    1.26 kB[22m[1m[22m[2m │ map:     2.04 kB[22m
[2m../src/webview-ui/build/[22m[36massets/xsl.js                           [39m[1m[2m    1.40 kB[22m[1m[22m[2m │ map:     2.20 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dotenv.js                        [39m[1m[2m    1.46 kB[22m[1m[22m[2m │ map:     2.35 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-D6G4REZN.js                [39m[1m[2m    1.46 kB[22m[1m[22m[2m │ map:     5.82 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sparql.js                        [39m[1m[2m    1.51 kB[22m[1m[22m[2m │ map:     2.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ini.js                           [39m[1m[2m    1.56 kB[22m[1m[22m[2m │ map:     2.49 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hxml.js                          [39m[1m[2m    1.77 kB[22m[1m[22m[2m │ map:     2.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/docker.js                        [39m[1m[2m    1.78 kB[22m[1m[22m[2m │ map:     2.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/desktop.js                       [39m[1m[2m    1.87 kB[22m[1m[22m[2m │ map:     2.78 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wenyan.js                        [39m[1m[2m    2.19 kB[22m[1m[22m[2m │ map:     3.20 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jssm.js                          [39m[1m[2m    2.27 kB[22m[1m[22m[2m │ map:     3.41 kB[22m
[2m../src/webview-ui/build/[22m[36massets/edge.js                          [39m[1m[2m    2.35 kB[22m[1m[22m[2m │ map:     3.56 kB[22m
[2m../src/webview-ui/build/[22m[36massets/reg.js                           [39m[1m[2m    2.38 kB[22m[1m[22m[2m │ map:     3.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/berry.js                         [39m[1m[2m    2.40 kB[22m[1m[22m[2m │ map:     3.84 kB[22m
[2m../src/webview-ui/build/[22m[36massets/erb.js                           [39m[1m[2m    2.47 kB[22m[1m[22m[2m │ map:     3.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/diff.js                          [39m[1m[2m    2.60 kB[22m[1m[22m[2m │ map:     3.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/_basePickBy.js                   [39m[1m[2m    2.60 kB[22m[1m[22m[2m │ map:    26.23 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gleam.js                         [39m[1m[2m    2.61 kB[22m[1m[22m[2m │ map:     4.01 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hy.js                            [39m[1m[2m    2.68 kB[22m[1m[22m[2m │ map:     3.71 kB[22m
[2m../src/webview-ui/build/[22m[36massets/json.js                          [39m[1m[2m    2.86 kB[22m[1m[22m[2m │ map:     4.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/log.js                           [39m[1m[2m    2.87 kB[22m[1m[22m[2m │ map:     4.36 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cairo.js                         [39m[1m[2m    2.96 kB[22m[1m[22m[2m │ map:     4.54 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jsonl.js                         [39m[1m[2m    3.05 kB[22m[1m[22m[2m │ map:     4.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jsonc.js                         [39m[1m[2m    3.14 kB[22m[1m[22m[2m │ map:     4.63 kB[22m
[2m../src/webview-ui/build/[22m[36massets/logo.js                          [39m[1m[2m    3.16 kB[22m[1m[22m[2m │ map:     3.83 kB[22m
[2m../src/webview-ui/build/[22m[36massets/po.js                            [39m[1m[2m    3.27 kB[22m[1m[22m[2m │ map:     5.10 kB[22m
[2m../src/webview-ui/build/[22m[36massets/json5.js                         [39m[1m[2m    3.29 kB[22m[1m[22m[2m │ map:     4.97 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mipsasm.js                       [39m[1m[2m    3.30 kB[22m[1m[22m[2m │ map:     4.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tasl.js                          [39m[1m[2m    3.32 kB[22m[1m[22m[2m │ map:     5.15 kB[22m
[2m../src/webview-ui/build/[22m[36massets/genie.js                         [39m[1m[2m    3.39 kB[22m[1m[22m[2m │ map:     4.89 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rel.js                           [39m[1m[2m    3.40 kB[22m[1m[22m[2m │ map:     4.89 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vala.js                          [39m[1m[2m    3.40 kB[22m[1m[22m[2m │ map:     4.91 kB[22m
[2m../src/webview-ui/build/[22m[36massets/arc.js                           [39m[1m[2m    3.44 kB[22m[1m[22m[2m │ map:    16.98 kB[22m
[2m../src/webview-ui/build/[22m[36massets/splunk.js                        [39m[1m[2m    3.47 kB[22m[1m[22m[2m │ map:     4.46 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fluent.js                        [39m[1m[2m    3.65 kB[22m[1m[22m[2m │ map:     5.22 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jsonnet.js                       [39m[1m[2m    3.65 kB[22m[1m[22m[2m │ map:     5.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ssh-config.js                    [39m[1m[2m    3.66 kB[22m[1m[22m[2m │ map:     4.51 kB[22m
[2m../src/webview-ui/build/[22m[36massets/glsl.js                          [39m[1m[2m    3.66 kB[22m[1m[22m[2m │ map:     4.39 kB[22m
[2m../src/webview-ui/build/[22m[36massets/diagram-VNBRO52H.js              [39m[1m[2m    3.69 kB[22m[1m[22m[2m │ map:    12.39 kB[22m
[2m../src/webview-ui/build/[22m[36massets/narrat.js                        [39m[1m[2m    3.71 kB[22m[1m[22m[2m │ map:     5.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/turtle.js                        [39m[1m[2m    3.74 kB[22m[1m[22m[2m │ map:     5.51 kB[22m
[2m../src/webview-ui/build/[22m[36massets/zenscript.js                     [39m[1m[2m    3.95 kB[22m[1m[22m[2m │ map:     5.77 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nextflow.js                      [39m[1m[2m    3.97 kB[22m[1m[22m[2m │ map:     5.96 kB[22m
[2m../src/webview-ui/build/[22m[36massets/lean.js                          [39m[1m[2m    4.13 kB[22m[1m[22m[2m │ map:     6.08 kB[22m
[2m../src/webview-ui/build/[22m[36massets/pascal.js                        [39m[1m[2m    4.19 kB[22m[1m[22m[2m │ map:     5.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fish.js                          [39m[1m[2m    4.21 kB[22m[1m[22m[2m │ map:     5.71 kB[22m
[2m../src/webview-ui/build/[22m[36massets/bicep.js                         [39m[1m[2m    4.34 kB[22m[1m[22m[2m │ map:     6.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tcl.js                           [39m[1m[2m    4.46 kB[22m[1m[22m[2m │ map:     6.50 kB[22m
[2m../src/webview-ui/build/[22m[36massets/http.js                          [39m[1m[2m    4.50 kB[22m[1m[22m[2m │ map:     6.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/polar.js                         [39m[1m[2m    4.71 kB[22m[1m[22m[2m │ map:     7.27 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sdbl.js                          [39m[1m[2m    4.74 kB[22m[1m[22m[2m │ map:     5.91 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fennel.js                        [39m[1m[2m    4.80 kB[22m[1m[22m[2m │ map:     6.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/bibtex.js                        [39m[1m[2m    4.83 kB[22m[1m[22m[2m │ map:     7.05 kB[22m
[2m../src/webview-ui/build/[22m[36massets/pieDiagram-IB7DONF6.js           [39m[1m[2m    4.90 kB[22m[1m[22m[2m │ map:    17.99 kB[22m
[2m../src/webview-ui/build/[22m[36massets/llvm.js                          [39m[1m[2m    5.05 kB[22m[1m[22m[2m │ map:     6.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wgsl.js                          [39m[1m[2m    5.18 kB[22m[1m[22m[2m │ map:     7.52 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gdresource.js                    [39m[1m[2m    5.28 kB[22m[1m[22m[2m │ map:     7.72 kB[22m
[2m../src/webview-ui/build/[22m[36massets/qml.js                           [39m[1m[2m    5.36 kB[22m[1m[22m[2m │ map:     8.14 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dax.js                           [39m[1m[2m    5.40 kB[22m[1m[22m[2m │ map:     6.78 kB[22m
[2m../src/webview-ui/build/[22m[36massets/zig.js                           [39m[1m[2m    5.40 kB[22m[1m[22m[2m │ map:     7.92 kB[22m
[2m../src/webview-ui/build/[22m[36massets/xml.js                           [39m[1m[2m    5.41 kB[22m[1m[22m[2m │ map:     7.86 kB[22m
[2m../src/webview-ui/build/[22m[36massets/awk.js                           [39m[1m[2m    5.49 kB[22m[1m[22m[2m │ map:     8.01 kB[22m
[2m../src/webview-ui/build/[22m[36massets/coq.js                           [39m[1m[2m    5.53 kB[22m[1m[22m[2m │ map:     7.50 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jinja.js                         [39m[1m[2m    5.69 kB[22m[1m[22m[2m │ map:     8.51 kB[22m
[2m../src/webview-ui/build/[22m[36massets/graph.js                         [39m[1m[2m    5.93 kB[22m[1m[22m[2m │ map:    23.03 kB[22m
[2m../src/webview-ui/build/[22m[36massets/powerquery.js                    [39m[1m[2m    5.94 kB[22m[1m[22m[2m │ map:     8.54 kB[22m
[2m../src/webview-ui/build/[22m[36massets/shaderlab.js                     [39m[1m[2m    5.95 kB[22m[1m[22m[2m │ map:     7.71 kB[22m
[2m../src/webview-ui/build/[22m[36massets/verilog.js                       [39m[1m[2m    5.97 kB[22m[1m[22m[2m │ map:     8.04 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cypher.js                        [39m[1m[2m    5.99 kB[22m[1m[22m[2m │ map:     8.46 kB[22m
[2m../src/webview-ui/build/[22m[36massets/diagram-SSKATNLV.js              [39m[1m[2m    6.01 kB[22m[1m[22m[2m │ map:    20.86 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vb.js                            [39m[1m[2m    6.12 kB[22m[1m[22m[2m │ map:     8.06 kB[22m
[2m../src/webview-ui/build/[22m[36massets/red.js                           [39m[1m[2m    6.29 kB[22m[1m[22m[2m │ map:     8.89 kB[22m
[2m../src/webview-ui/build/[22m[36massets/min-dark.js                      [39m[1m[2m    6.33 kB[22m[1m[22m[2m │ map:     8.63 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gdshader.js                      [39m[1m[2m    6.36 kB[22m[1m[22m[2m │ map:     9.21 kB[22m
[2m../src/webview-ui/build/[22m[36massets/prisma.js                        [39m[1m[2m    6.37 kB[22m[1m[22m[2m │ map:     9.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ara.js                           [39m[1m[2m    6.39 kB[22m[1m[22m[2m │ map:     9.27 kB[22m
[2m../src/webview-ui/build/[22m[36massets/clojure.js                       [39m[1m[2m    6.45 kB[22m[1m[22m[2m │ map:     9.31 kB[22m
[2m../src/webview-ui/build/[22m[36massets/postcss.js                       [39m[1m[2m    6.45 kB[22m[1m[22m[2m │ map:     9.13 kB[22m
[2m../src/webview-ui/build/[22m[36massets/toml.js                          [39m[1m[2m    6.46 kB[22m[1m[22m[2m │ map:     9.49 kB[22m
[2m../src/webview-ui/build/[22m[36massets/solarized-light.js               [39m[1m[2m    6.52 kB[22m[1m[22m[2m │ map:     9.25 kB[22m
[2m../src/webview-ui/build/[22m[36massets/proto.js                         [39m[1m[2m    6.56 kB[22m[1m[22m[2m │ map:     9.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/smalltalk.js                     [39m[1m[2m    6.63 kB[22m[1m[22m[2m │ map:     9.27 kB[22m
[2m../src/webview-ui/build/[22m[36massets/talonscript.js                   [39m[1m[2m    6.80 kB[22m[1m[22m[2m │ map:    10.10 kB[22m
[2m../src/webview-ui/build/[22m[36massets/solarized-dark.js                [39m[1m[2m    6.89 kB[22m[1m[22m[2m │ map:     9.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/riscv.js                         [39m[1m[2m    6.95 kB[22m[1m[22m[2m │ map:     9.62 kB[22m
[2m../src/webview-ui/build/[22m[36massets/soy.js                           [39m[1m[2m    6.99 kB[22m[1m[22m[2m │ map:    10.72 kB[22m
[2m../src/webview-ui/build/[22m[36massets/min-light.js                     [39m[1m[2m    7.01 kB[22m[1m[22m[2m │ map:     9.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/scheme.js                        [39m[1m[2m    7.20 kB[22m[1m[22m[2m │ map:     9.81 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hlsl.js                          [39m[1m[2m    7.30 kB[22m[1m[22m[2m │ map:     9.26 kB[22m
[2m../src/webview-ui/build/[22m[36massets/qss.js                           [39m[1m[2m    7.50 kB[22m[1m[22m[2m │ map:     9.42 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dart.js                          [39m[1m[2m    7.85 kB[22m[1m[22m[2m │ map:    11.54 kB[22m
[2m../src/webview-ui/build/[22m[36massets/systemd.js                       [39m[1m[2m    7.91 kB[22m[1m[22m[2m │ map:    10.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/monokai.js                       [39m[1m[2m    7.92 kB[22m[1m[22m[2m │ map:    11.11 kB[22m
[2m../src/webview-ui/build/[22m[36massets/regexp.js                        [39m[1m[2m    8.02 kB[22m[1m[22m[2m │ map:    11.21 kB[22m
[2m../src/webview-ui/build/[22m[36massets/haml.js                          [39m[1m[2m    8.28 kB[22m[1m[22m[2m │ map:    12.38 kB[22m
[2m../src/webview-ui/build/[22m[36massets/typst.js                         [39m[1m[2m    8.42 kB[22m[1m[22m[2m │ map:    12.32 kB[22m
[2m../src/webview-ui/build/[22m[36massets/plsql.js                         [39m[1m[2m    8.55 kB[22m[1m[22m[2m │ map:    10.91 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vue-html.js                      [39m[1m[2m    8.64 kB[22m[1m[22m[2m │ map:    12.06 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kotlin.js                        [39m[1m[2m    8.82 kB[22m[1m[22m[2m │ map:    12.80 kB[22m
[2m../src/webview-ui/build/[22m[36massets/andromeeda.js                    [39m[1m[2m    8.90 kB[22m[1m[22m[2m │ map:    11.77 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ts-tags.js                       [39m[1m[2m    8.90 kB[22m[1m[22m[2m │ map:    14.49 kB[22m
[2m../src/webview-ui/build/[22m[36massets/make.js                          [39m[1m[2m    8.99 kB[22m[1m[22m[2m │ map:    12.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sas.js                           [39m[1m[2m    9.09 kB[22m[1m[22m[2m │ map:    11.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dark-plus.js                     [39m[1m[2m    9.14 kB[22m[1m[22m[2m │ map:    12.43 kB[22m
[2m../src/webview-ui/build/[22m[36massets/slack-dark.js                    [39m[1m[2m    9.16 kB[22m[1m[22m[2m │ map:    12.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sass.js                          [39m[1m[2m    9.32 kB[22m[1m[22m[2m │ map:    13.44 kB[22m
[2m../src/webview-ui/build/[22m[36massets/plastic.js                       [39m[1m[2m    9.33 kB[22m[1m[22m[2m │ map:    12.50 kB[22m
[2m../src/webview-ui/build/[22m[36massets/slack-ochin.js                   [39m[1m[2m    9.47 kB[22m[1m[22m[2m │ map:    13.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tex.js                           [39m[1m[2m    9.55 kB[22m[1m[22m[2m │ map:    12.22 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jison.js                         [39m[1m[2m    9.72 kB[22m[1m[22m[2m │ map:    14.11 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cmake.js                         [39m[1m[2m    9.89 kB[22m[1m[22m[2m │ map:    11.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/light-plus.js                    [39m[1m[2m    9.98 kB[22m[1m[22m[2m │ map:    13.43 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hcl.js                           [39m[1m[2m   10.09 kB[22m[1m[22m[2m │ map:    14.81 kB[22m
[2m../src/webview-ui/build/[22m[36massets/beancount.js                     [39m[1m[2m   10.17 kB[22m[1m[22m[2m │ map:    14.74 kB[22m
[2m../src/webview-ui/build/[22m[36massets/linear.js                        [39m[1m[2m   10.19 kB[22m[1m[22m[2m │ map:    50.09 kB[22m
[2m../src/webview-ui/build/[22m[36massets/raku.js                          [39m[1m[2m   10.51 kB[22m[1m[22m[2m │ map:    12.93 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rst.js                           [39m[1m[2m   10.51 kB[22m[1m[22m[2m │ map:    15.58 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dream-maker.js                   [39m[1m[2m   10.51 kB[22m[1m[22m[2m │ map:    15.14 kB[22m
[2m../src/webview-ui/build/[22m[36massets/yaml.js                          [39m[1m[2m   10.54 kB[22m[1m[22m[2m │ map:    14.92 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cadence.js                       [39m[1m[2m   10.54 kB[22m[1m[22m[2m │ map:    15.19 kB[22m
[2m../src/webview-ui/build/[22m[36massets/stateDiagram-DGXRK772.js         [39m[1m[2m   10.60 kB[22m[1m[22m[2m │ map:    36.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/elm.js                           [39m[1m[2m   10.98 kB[22m[1m[22m[2m │ map:    16.05 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dagre-OKDRZEBW.js                [39m[1m[2m   11.22 kB[22m[1m[22m[2m │ map:    41.98 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-light.js                  [39m[1m[2m   11.23 kB[22m[1m[22m[2m │ map:    15.43 kB[22m
[2m../src/webview-ui/build/[22m[36massets/prolog.js                        [39m[1m[2m   11.39 kB[22m[1m[22m[2m │ map:    13.32 kB[22m
[2m../src/webview-ui/build/[22m[36massets/terraform.js                     [39m[1m[2m   11.43 kB[22m[1m[22m[2m │ map:    16.24 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-dark.js                   [39m[1m[2m   11.45 kB[22m[1m[22m[2m │ map:    15.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/puppet.js                        [39m[1m[2m   11.47 kB[22m[1m[22m[2m │ map:    16.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/laserwave.js                     [39m[1m[2m   11.54 kB[22m[1m[22m[2m │ map:    14.72 kB[22m
[2m../src/webview-ui/build/[22m[36massets/_baseUniq.js                     [39m[1m[2m   11.93 kB[22m[1m[22m[2m │ map:   106.64 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gherkin.js                       [39m[1m[2m   11.98 kB[22m[1m[22m[2m │ map:    13.15 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wasm.js                          [39m[1m[2m   12.04 kB[22m[1m[22m[2m │ map:    16.86 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hjson.js                         [39m[1m[2m   12.09 kB[22m[1m[22m[2m │ map:    17.31 kB[22m
[2m../src/webview-ui/build/[22m[36massets/handlebars.js                    [39m[1m[2m   12.15 kB[22m[1m[22m[2m │ map:    17.39 kB[22m
[2m../src/webview-ui/build/[22m[36massets/apache.js                        [39m[1m[2m   12.49 kB[22m[1m[22m[2m │ map:    15.97 kB[22m
[2m../src/webview-ui/build/[22m[36massets/luau.js                          [39m[1m[2m   12.72 kB[22m[1m[22m[2m │ map:    17.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vesper.js                        [39m[1m[2m   12.73 kB[22m[1m[22m[2m │ map:    16.18 kB[22m
[2m../src/webview-ui/build/[22m[36massets/bat.js                           [39m[1m[2m   12.92 kB[22m[1m[22m[2m │ map:    17.42 kB[22m
[2m../src/webview-ui/build/[22m[36massets/v.js                             [39m[1m[2m   13.26 kB[22m[1m[22m[2m │ map:    19.31 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vitesse-light.js                 [39m[1m[2m   13.66 kB[22m[1m[22m[2m │ map:    18.56 kB[22m
[2m../src/webview-ui/build/[22m[36massets/aurora-x.js                      [39m[1m[2m   13.70 kB[22m[1m[22m[2m │ map:    17.59 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vitesse-black.js                 [39m[1m[2m   13.72 kB[22m[1m[22m[2m │ map:    18.65 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vitesse-dark.js                  [39m[1m[2m   13.80 kB[22m[1m[22m[2m │ map:    18.73 kB[22m
[2m../src/webview-ui/build/[22m[36massets/pug.js                           [39m[1m[2m   13.85 kB[22m[1m[22m[2m │ map:    20.52 kB[22m
[2m../src/webview-ui/build/[22m[36massets/synthwave-84.js                  [39m[1m[2m   14.08 kB[22m[1m[22m[2m │ map:    19.11 kB[22m
[2m../src/webview-ui/build/[22m[36massets/actionscript-3.js                [39m[1m[2m   14.09 kB[22m[1m[22m[2m │ map:    19.86 kB[22m
[2m../src/webview-ui/build/[22m[36massets/clarity.js                       [39m[1m[2m   14.14 kB[22m[1m[22m[2m │ map:    19.78 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-light-default.js          [39m[1m[2m   14.21 kB[22m[1m[22m[2m │ map:    19.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-light-high-contrast.js    [39m[1m[2m   14.33 kB[22m[1m[22m[2m │ map:    19.47 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-dark-dimmed.js            [39m[1m[2m   14.48 kB[22m[1m[22m[2m │ map:    19.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-dark-default.js           [39m[1m[2m   14.48 kB[22m[1m[22m[2m │ map:    19.67 kB[22m
[2m../src/webview-ui/build/[22m[36massets/github-dark-high-contrast.js     [39m[1m[2m   14.65 kB[22m[1m[22m[2m │ map:    19.90 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nix.js                           [39m[1m[2m   14.78 kB[22m[1m[22m[2m │ map:    21.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gnuplot.js                       [39m[1m[2m   14.82 kB[22m[1m[22m[2m │ map:    20.84 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ayu-dark.js                      [39m[1m[2m   14.99 kB[22m[1m[22m[2m │ map:    20.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rust.js                          [39m[1m[2m   15.10 kB[22m[1m[22m[2m │ map:    21.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kusto.js                         [39m[1m[2m   15.20 kB[22m[1m[22m[2m │ map:    19.63 kB[22m
[2m../src/webview-ui/build/[22m[36massets/lua.js                           [39m[1m[2m   15.23 kB[22m[1m[22m[2m │ map:    21.65 kB[22m
[2m../src/webview-ui/build/[22m[36massets/abap.js                          [39m[1m[2m   15.89 kB[22m[1m[22m[2m │ map:    18.86 kB[22m
[2m../src/webview-ui/build/[22m[36massets/matlab.js                        [39m[1m[2m   16.12 kB[22m[1m[22m[2m │ map:    23.39 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cue.js                           [39m[1m[2m   16.24 kB[22m[1m[22m[2m │ map:    23.81 kB[22m
[2m../src/webview-ui/build/[22m[36massets/solidity.js                      [39m[1m[2m   16.28 kB[22m[1m[22m[2m │ map:    23.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/elixir.js                        [39m[1m[2m   16.33 kB[22m[1m[22m[2m │ map:    23.15 kB[22m
[2m../src/webview-ui/build/[22m[36massets/marko.js                         [39m[1m[2m   16.72 kB[22m[1m[22m[2m │ map:    24.04 kB[22m
[2m../src/webview-ui/build/[22m[36massets/svelte.js                        [39m[1m[2m   16.96 kB[22m[1m[22m[2m │ map:    23.91 kB[22m
[2m../src/webview-ui/build/[22m[36massets/move.js                          [39m[1m[2m   17.10 kB[22m[1m[22m[2m │ map:    25.65 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kanagawa-wave.js                 [39m[1m[2m   17.17 kB[22m[1m[22m[2m │ map:    22.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kanagawa-lotus.js                [39m[1m[2m   17.17 kB[22m[1m[22m[2m │ map:    22.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kanagawa-dragon.js               [39m[1m[2m   17.17 kB[22m[1m[22m[2m │ map:    22.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/liquid.js                        [39m[1m[2m   17.72 kB[22m[1m[22m[2m │ map:    24.77 kB[22m
[2m../src/webview-ui/build/[22m[36massets/graphql.js                       [39m[1m[2m   18.00 kB[22m[1m[22m[2m │ map:    25.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/material-theme.js                [39m[1m[2m   18.66 kB[22m[1m[22m[2m │ map:    24.13 kB[22m
[2m../src/webview-ui/build/[22m[36massets/material-theme-ocean.js          [39m[1m[2m   18.68 kB[22m[1m[22m[2m │ map:    24.17 kB[22m
[2m../src/webview-ui/build/[22m[36massets/material-theme-darker.js         [39m[1m[2m   18.68 kB[22m[1m[22m[2m │ map:    24.17 kB[22m
[2m../src/webview-ui/build/[22m[36massets/material-theme-lighter.js        [39m[1m[2m   18.69 kB[22m[1m[22m[2m │ map:    24.18 kB[22m
[2m../src/webview-ui/build/[22m[36massets/material-theme-palenight.js      [39m[1m[2m   18.69 kB[22m[1m[22m[2m │ map:    24.19 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gdscript.js                      [39m[1m[2m   18.87 kB[22m[1m[22m[2m │ map:    26.11 kB[22m
[2m../src/webview-ui/build/[22m[36massets/groovy.js                        [39m[1m[2m   19.21 kB[22m[1m[22m[2m │ map:    27.54 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mdc.js                           [39m[1m[2m   19.61 kB[22m[1m[22m[2m │ map:    23.10 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nushell.js                       [39m[1m[2m   19.84 kB[22m[1m[22m[2m │ map:    27.04 kB[22m
[2m../src/webview-ui/build/[22m[36massets/glimmer-js.js                    [39m[1m[2m   20.07 kB[22m[1m[22m[2m │ map:    28.61 kB[22m
[2m../src/webview-ui/build/[22m[36massets/glimmer-ts.js                    [39m[1m[2m   20.07 kB[22m[1m[22m[2m │ map:    28.61 kB[22m
[2m../src/webview-ui/build/[22m[36massets/powershell.js                    [39m[1m[2m   20.19 kB[22m[1m[22m[2m │ map:    26.62 kB[22m
[2m../src/webview-ui/build/[22m[36massets/viml.js                          [39m[1m[2m   20.40 kB[22m[1m[22m[2m │ map:    23.52 kB[22m
[2m../src/webview-ui/build/[22m[36massets/kanban-definition-NDS4AKOZ.js    [39m[1m[2m   20.55 kB[22m[1m[22m[2m │ map:    68.26 kB[22m
[2m../src/webview-ui/build/[22m[36massets/snazzy-light.js                  [39m[1m[2m   20.81 kB[22m[1m[22m[2m │ map:    28.02 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vue.js                           [39m[1m[2m   21.08 kB[22m[1m[22m[2m │ map:    31.76 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dracula.js                       [39m[1m[2m   21.10 kB[22m[1m[22m[2m │ map:    27.51 kB[22m
[2m../src/webview-ui/build/[22m[36massets/dracula-soft.js                  [39m[1m[2m   21.12 kB[22m[1m[22m[2m │ map:    27.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/twig.js                          [39m[1m[2m   21.19 kB[22m[1m[22m[2m │ map:    29.23 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wit.js                           [39m[1m[2m   21.50 kB[22m[1m[22m[2m │ map:    30.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rose-pine.js                     [39m[1m[2m   21.78 kB[22m[1m[22m[2m │ map:    28.78 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rose-pine-moon.js                [39m[1m[2m   21.80 kB[22m[1m[22m[2m │ map:    28.81 kB[22m
[2m../src/webview-ui/build/[22m[36massets/rose-pine-dawn.js                [39m[1m[2m   21.80 kB[22m[1m[22m[2m │ map:    28.81 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-dark-hard.js             [39m[1m[2m   21.90 kB[22m[1m[22m[2m │ map:    29.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-dark-soft.js             [39m[1m[2m   21.90 kB[22m[1m[22m[2m │ map:    29.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-light-hard.js            [39m[1m[2m   21.91 kB[22m[1m[22m[2m │ map:    29.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-light-soft.js            [39m[1m[2m   21.91 kB[22m[1m[22m[2m │ map:    29.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-dark-medium.js           [39m[1m[2m   21.91 kB[22m[1m[22m[2m │ map:    29.54 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gruvbox-light-medium.js          [39m[1m[2m   21.91 kB[22m[1m[22m[2m │ map:    29.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sankeyDiagram-QLVOVGJD.js        [39m[1m[2m   22.17 kB[22m[1m[22m[2m │ map:    85.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nim.js                           [39m[1m[2m   22.42 kB[22m[1m[22m[2m │ map:    32.76 kB[22m
[2m../src/webview-ui/build/[22m[36massets/common-lisp.js                   [39m[1m[2m   22.62 kB[22m[1m[22m[2m │ map:    26.71 kB[22m
[2m../src/webview-ui/build/[22m[36massets/journeyDiagram-U35MCT3I.js       [39m[1m[2m   22.69 kB[22m[1m[22m[2m │ map:    76.75 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sql.js                           [39m[1m[2m   23.45 kB[22m[1m[22m[2m │ map:    27.95 kB[22m
[2m../src/webview-ui/build/[22m[36massets/purescript.js                    [39m[1m[2m   23.60 kB[22m[1m[22m[2m │ map:    33.41 kB[22m
[2m../src/webview-ui/build/[22m[36massets/timeline-definition-BDJGKUSR.js  [39m[1m[2m   23.64 kB[22m[1m[22m[2m │ map:    82.16 kB[22m
[2m../src/webview-ui/build/[22m[36massets/typespec.js                      [39m[1m[2m   23.69 kB[22m[1m[22m[2m │ map:    33.08 kB[22m
[2m../src/webview-ui/build/[22m[36massets/astro.js                         [39m[1m[2m   23.96 kB[22m[1m[22m[2m │ map:    29.10 kB[22m
[2m../src/webview-ui/build/[22m[36massets/templ.js                         [39m[1m[2m   24.00 kB[22m[1m[22m[2m │ map:    30.82 kB[22m
[2m../src/webview-ui/build/[22m[36massets/apl.js                           [39m[1m[2m   24.02 kB[22m[1m[22m[2m │ map:    33.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vhdl.js                          [39m[1m[2m   24.30 kB[22m[1m[22m[2m │ map:    33.40 kB[22m
[2m../src/webview-ui/build/[22m[36massets/angular-html.js                  [39m[1m[2m   24.32 kB[22m[1m[22m[2m │ map:    35.57 kB[22m
[2m../src/webview-ui/build/[22m[36massets/gitGraphDiagram-7IBYFJ6S.js      [39m[1m[2m   24.61 kB[22m[1m[22m[2m │ map:   102.61 kB[22m
[2m../src/webview-ui/build/[22m[36massets/erDiagram-Q7BY3M3F.js            [39m[1m[2m   25.28 kB[22m[1m[22m[2m │ map:    85.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fsharp.js                        [39m[1m[2m   25.32 kB[22m[1m[22m[2m │ map:    35.88 kB[22m
[2m../src/webview-ui/build/[22m[36massets/one-light.js                     [39m[1m[2m   25.34 kB[22m[1m[22m[2m │ map:    33.80 kB[22m
[2m../src/webview-ui/build/[22m[36massets/razor.js                         [39m[1m[2m   25.56 kB[22m[1m[22m[2m │ map:    35.73 kB[22m
[2m../src/webview-ui/build/[22m[36massets/system-verilog.js                [39m[1m[2m   26.25 kB[22m[1m[22m[2m │ map:    36.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nord.js                          [39m[1m[2m   26.76 kB[22m[1m[22m[2m │ map:    35.35 kB[22m
[2m../src/webview-ui/build/[22m[36massets/codeql.js                        [39m[1m[2m   26.92 kB[22m[1m[22m[2m │ map:    36.03 kB[22m
[2m../src/webview-ui/build/[22m[36massets/layout.js                        [39m[1m[2m   27.20 kB[22m[1m[22m[2m │ map:   162.85 kB[22m
[2m../src/webview-ui/build/[22m[36massets/scss.js                          [39m[1m[2m   27.23 kB[22m[1m[22m[2m │ map:    38.34 kB[22m
[2m../src/webview-ui/build/[22m[36massets/java.js                          [39m[1m[2m   27.25 kB[22m[1m[22m[2m │ map:    38.65 kB[22m
[2m../src/webview-ui/build/[22m[36massets/coffee.js                        [39m[1m[2m   27.45 kB[22m[1m[22m[2m │ map:    36.09 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mermaid.js                       [39m[1m[2m   28.32 kB[22m[1m[22m[2m │ map:    42.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/scala.js                         [39m[1m[2m   28.92 kB[22m[1m[22m[2m │ map:    40.13 kB[22m
[2m../src/webview-ui/build/[22m[36massets/night-owl.js                     [39m[1m[2m   28.95 kB[22m[1m[22m[2m │ map:    39.06 kB[22m
[2m../src/webview-ui/build/[22m[36massets/crystal.js                       [39m[1m[2m   29.37 kB[22m[1m[22m[2m │ map:    41.18 kB[22m
[2m../src/webview-ui/build/[22m[36massets/applescript.js                   [39m[1m[2m   29.61 kB[22m[1m[22m[2m │ map:    39.37 kB[22m
[2m../src/webview-ui/build/[22m[36massets/requirementDiagram-KVF5MWMF.js   [39m[1m[2m   30.22 kB[22m[1m[22m[2m │ map:    96.75 kB[22m
[2m../src/webview-ui/build/[22m[36massets/julia.js                         [39m[1m[2m   31.03 kB[22m[1m[22m[2m │ map:    40.56 kB[22m
[2m../src/webview-ui/build/[22m[36massets/stylus.js                        [39m[1m[2m   31.11 kB[22m[1m[22m[2m │ map:    38.89 kB[22m
[2m../src/webview-ui/build/[22m[36massets/poimandres.js                    [39m[1m[2m   33.53 kB[22m[1m[22m[2m │ map:    43.07 kB[22m
[2m../src/webview-ui/build/[22m[36massets/one-dark-pro.js                  [39m[1m[2m   33.83 kB[22m[1m[22m[2m │ map:    44.50 kB[22m
[2m../src/webview-ui/build/[22m[36massets/bsl.js                           [39m[1m[2m   33.89 kB[22m[1m[22m[2m │ map:    37.61 kB[22m
[2m../src/webview-ui/build/[22m[36massets/quadrantDiagram-7GDLP6J5.js      [39m[1m[2m   34.28 kB[22m[1m[22m[2m │ map:   111.05 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-AEK57VVT.js                [39m[1m[2m   35.09 kB[22m[1m[22m[2m │ map:   119.00 kB[22m
[2m../src/webview-ui/build/[22m[36massets/haxe.js                          [39m[1m[2m   35.19 kB[22m[1m[22m[2m │ map:    49.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/nginx.js                         [39m[1m[2m   35.39 kB[22m[1m[22m[2m │ map:    48.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/houston.js                       [39m[1m[2m   35.46 kB[22m[1m[22m[2m │ map:    46.40 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tokyo-night.js                   [39m[1m[2m   35.71 kB[22m[1m[22m[2m │ map:    45.35 kB[22m
[2m../src/webview-ui/build/[22m[36massets/howler.js                        [39m[1m[2m   36.94 kB[22m[1m[22m[2m │ map:   156.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/erlang.js                        [39m[1m[2m   37.47 kB[22m[1m[22m[2m │ map:    51.49 kB[22m
[2m../src/webview-ui/build/[22m[36massets/xychartDiagram-VJFVF3MP.js       [39m[1m[2m   38.70 kB[22m[1m[22m[2m │ map:   127.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cobol.js                         [39m[1m[2m   39.13 kB[22m[1m[22m[2m │ map:    47.37 kB[22m
[2m../src/webview-ui/build/[22m[36massets/r.js                             [39m[1m[2m   39.46 kB[22m[1m[22m[2m │ map:    48.57 kB[22m
[2m../src/webview-ui/build/[22m[36massets/asm.js                           [39m[1m[2m   40.75 kB[22m[1m[22m[2m │ map:    53.01 kB[22m
[2m../src/webview-ui/build/[22m[36massets/shellscript.js                   [39m[1m[2m   41.52 kB[22m[1m[22m[2m │ map:    56.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/haskell.js                       [39m[1m[2m   41.53 kB[22m[1m[22m[2m │ map:    57.87 kB[22m
[2m../src/webview-ui/build/[22m[36massets/perl.js                          [39m[1m[2m   43.14 kB[22m[1m[22m[2m │ map:    60.75 kB[22m
[2m../src/webview-ui/build/[22m[36massets/d.js                             [39m[1m[2m   43.83 kB[22m[1m[22m[2m │ map:    62.70 kB[22m
[2m../src/webview-ui/build/[22m[36massets/chunk-A2AXSNBT.js                [39m[1m[2m   45.15 kB[22m[1m[22m[2m │ map:   139.53 kB[22m
[2m../src/webview-ui/build/[22m[36massets/go.js                            [39m[1m[2m   45.16 kB[22m[1m[22m[2m │ map:    64.21 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ruby.js                          [39m[1m[2m   45.78 kB[22m[1m[22m[2m │ map:    65.25 kB[22m
[2m../src/webview-ui/build/[22m[36massets/apex.js                          [39m[1m[2m   46.11 kB[22m[1m[22m[2m │ map:    64.40 kB[22m
[2m../src/webview-ui/build/[22m[36massets/catppuccin-mocha.js              [39m[1m[2m   46.89 kB[22m[1m[22m[2m │ map:    61.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/catppuccin-latte.js              [39m[1m[2m   46.89 kB[22m[1m[22m[2m │ map:    61.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/catppuccin-frappe.js             [39m[1m[2m   46.89 kB[22m[1m[22m[2m │ map:    61.48 kB[22m
[2m../src/webview-ui/build/[22m[36massets/catppuccin-macchiato.js          [39m[1m[2m   46.90 kB[22m[1m[22m[2m │ map:    61.50 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ada.js                           [39m[1m[2m   48.11 kB[22m[1m[22m[2m │ map:    70.98 kB[22m
[2m../src/webview-ui/build/[22m[36massets/css.js                           [39m[1m[2m   49.06 kB[22m[1m[22m[2m │ map:    60.64 kB[22m
[2m../src/webview-ui/build/[22m[36massets/imba.js                          [39m[1m[2m   49.96 kB[22m[1m[22m[2m │ map:    68.10 kB[22m
[2m../src/webview-ui/build/[22m[36massets/everforest-dark.js               [39m[1m[2m   53.79 kB[22m[1m[22m[2m │ map:    68.27 kB[22m
[2m../src/webview-ui/build/[22m[36massets/everforest-light.js              [39m[1m[2m   53.79 kB[22m[1m[22m[2m │ map:    68.28 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wikitext.js                      [39m[1m[2m   55.92 kB[22m[1m[22m[2m │ map:    77.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/markdown.js                      [39m[1m[2m   55.95 kB[22m[1m[22m[2m │ map:    77.62 kB[22m
[2m../src/webview-ui/build/[22m[36massets/stata.js                         [39m[1m[2m   57.02 kB[22m[1m[22m[2m │ map:    74.18 kB[22m
[2m../src/webview-ui/build/[22m[36massets/html.js                          [39m[1m[2m   57.26 kB[22m[1m[22m[2m │ map:    73.00 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ballerina.js                     [39m[1m[2m   58.73 kB[22m[1m[22m[2m │ map:    81.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/flowDiagram-4HSFHLVR.js          [39m[1m[2m   60.82 kB[22m[1m[22m[2m │ map:   189.77 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ganttDiagram-APWFNJXF.js         [39m[1m[2m   62.08 kB[22m[1m[22m[2m │ map:   229.52 kB[22m
[2m../src/webview-ui/build/[22m[36massets/ocaml.js                         [39m[1m[2m   62.48 kB[22m[1m[22m[2m │ map:    81.12 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mojo.js                          [39m[1m[2m   69.32 kB[22m[1m[22m[2m │ map:    94.24 kB[22m
[2m../src/webview-ui/build/[22m[36massets/latex.js                         [39m[1m[2m   69.42 kB[22m[1m[22m[2m │ map:    96.15 kB[22m
[2m../src/webview-ui/build/[22m[36massets/python.js                        [39m[1m[2m   69.99 kB[22m[1m[22m[2m │ map:    95.26 kB[22m
[2m../src/webview-ui/build/[22m[36massets/c4Diagram-VJAJSXHY.js            [39m[1m[2m   70.15 kB[22m[1m[22m[2m │ map:   204.04 kB[22m
[2m../src/webview-ui/build/[22m[36massets/c.js                             [39m[1m[2m   72.14 kB[22m[1m[22m[2m │ map:    95.40 kB[22m
[2m../src/webview-ui/build/[22m[36massets/blockDiagram-JOT3LUYC.js         [39m[1m[2m   72.78 kB[22m[1m[22m[2m │ map:   247.09 kB[22m
[2m../src/webview-ui/build/[22m[36massets/vyper.js                         [39m[1m[2m   74.68 kB[22m[1m[22m[2m │ map:   100.72 kB[22m
[2m../src/webview-ui/build/[22m[36massets/hack.js                          [39m[1m[2m   80.23 kB[22m[1m[22m[2m │ map:    97.30 kB[22m
[2m../src/webview-ui/build/[22m[36massets/csharp.js                        [39m[1m[2m   85.62 kB[22m[1m[22m[2m │ map:   120.18 kB[22m
[2m../src/webview-ui/build/[22m[36massets/swift.js                         [39m[1m[2m   86.00 kB[22m[1m[22m[2m │ map:   113.14 kB[22m
[2m../src/webview-ui/build/[22m[36massets/asciidoc.js                      [39m[1m[2m   87.10 kB[22m[1m[22m[2m │ map:   124.90 kB[22m
[2m../src/webview-ui/build/[22m[36massets/fortran-free-form.js             [39m[1m[2m   87.19 kB[22m[1m[22m[2m │ map:   121.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/sequenceDiagram-X6HHIX6F.js      [39m[1m[2m   88.58 kB[22m[1m[22m[2m │ map:   261.65 kB[22m
[2m../src/webview-ui/build/[22m[36massets/racket.js                        [39m[1m[2m   92.42 kB[22m[1m[22m[2m │ map:   106.20 kB[22m
[2m../src/webview-ui/build/[22m[36massets/less.js                          [39m[1m[2m   97.67 kB[22m[1m[22m[2m │ map:   131.87 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mindmap-definition-ALO5MXBD.js   [39m[1m[2m  101.03 kB[22m[1m[22m[2m │ map:   367.83 kB[22m
[2m../src/webview-ui/build/[22m[36massets/blade.js                         [39m[1m[2m  103.77 kB[22m[1m[22m[2m │ map:   129.26 kB[22m
[2m../src/webview-ui/build/[22m[36massets/objective-c.js                   [39m[1m[2m  105.45 kB[22m[1m[22m[2m │ map:   129.67 kB[22m
[2m../src/webview-ui/build/[22m[36massets/php.js                           [39m[1m[2m  110.97 kB[22m[1m[22m[2m │ map:   139.29 kB[22m
[2m../src/webview-ui/build/[22m[36massets/mdx.js                           [39m[1m[2m  136.15 kB[22m[1m[22m[2m │ map:   176.74 kB[22m
[2m../src/webview-ui/build/[22m[36massets/architectureDiagram-IEHRJDOE.js  [39m[1m[2m  148.35 kB[22m[1m[22m[2m │ map:   591.56 kB[22m
[2m../src/webview-ui/build/[22m[36massets/objective-cpp.js                 [39m[1m[2m  172.01 kB[22m[1m[22m[2m │ map:   218.55 kB[22m
[2m../src/webview-ui/build/[22m[36massets/javascript.js                    [39m[1m[2m  174.85 kB[22m[1m[22m[2m │ map:   236.06 kB[22m
[2m../src/webview-ui/build/[22m[36massets/tsx.js                           [39m[1m[2m  175.57 kB[22m[1m[22m[2m │ map:   236.75 kB[22m
[2m../src/webview-ui/build/[22m[36massets/jsx.js                           [39m[1m[2m  177.82 kB[22m[1m[22m[2m │ map:   239.00 kB[22m
[2m../src/webview-ui/build/[22m[36massets/typescript.js                    [39m[1m[2m  181.11 kB[22m[1m[22m[2m │ map:   244.40 kB[22m
[2m../src/webview-ui/build/[22m[36massets/angular-ts.js                    [39m[1m[2m  183.81 kB[22m[1m[22m[2m │ map:   249.41 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wolfram.js                       [39m[1m[2m  262.43 kB[22m[1m[22m[2m │ map:   279.67 kB[22m
[2m../src/webview-ui/build/[22m[36massets/katex.js                         [39m[1m[2m  265.48 kB[22m[1m[22m[2m │ map:   988.24 kB[22m
[2m../src/webview-ui/build/[22m[36massets/radar-MK3ICKWK.js                [39m[1m[2m  315.31 kB[22m[1m[22m[2m │ map: 1,333.66 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cytoscape.esm.js                 [39m[1m[2m  438.72 kB[22m[1m[22m[2m │ map: 1,846.73 kB[22m
[2m../src/webview-ui/build/[22m[36massets/wasm2.js                         [39m[1m[33m  622.37 kB[39m[22m[2m │ map:   622.93 kB[22m
[2m../src/webview-ui/build/[22m[36massets/cpp.js                           [39m[1m[33m  626.08 kB[39m[22m[2m │ map:   816.14 kB[22m
[2m../src/webview-ui/build/[22m[36massets/emacs-lisp.js                    [39m[1m[33m  779.89 kB[39m[22m[2m │ map:   789.83 kB[22m
[2m../src/webview-ui/build/[22m[36massets/index.js                         [39m[1m[33m3,514.81 kB[39m[22m[2m │ map: 9,213.22 kB[22m
[33m
(!) Some chunks are larger than 500 kB after minification. Consider:
- Using dynamic import() to code-split the application
- Use build.rollupOptions.output.manualChunks to improve chunking: https://rollupjs.org/configuration-options/#output-manualchunks
- Adjust chunk size limit for this warning via build.chunkSizeWarningLimit.[39m
[32m✓ built in 22.89s[39m
