hoistPattern:
  - '*'
hoistedDependencies:
  '@adobe/css-tools@4.4.2':
    '@adobe/css-tools': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@anthropic-ai/bedrock-sdk@0.10.4':
    '@anthropic-ai/bedrock-sdk': private
  '@anthropic-ai/sdk@0.37.0':
    '@anthropic-ai/sdk': private
  '@anthropic-ai/vertex-sdk@0.7.0':
    '@anthropic-ai/vertex-sdk': private
  '@aws-crypto/crc32@5.2.0':
    '@aws-crypto/crc32': private
  '@aws-crypto/sha256-browser@5.2.0':
    '@aws-crypto/sha256-browser': private
  '@aws-crypto/sha256-js@4.0.0':
    '@aws-crypto/sha256-js': private
  '@aws-crypto/supports-web-crypto@5.2.0':
    '@aws-crypto/supports-web-crypto': private
  '@aws-crypto/util@5.2.0':
    '@aws-crypto/util': private
  '@aws-sdk/client-bedrock-runtime@3.817.0':
    '@aws-sdk/client-bedrock-runtime': private
  '@aws-sdk/client-cognito-identity@3.817.0':
    '@aws-sdk/client-cognito-identity': private
  '@aws-sdk/client-sso@3.817.0':
    '@aws-sdk/client-sso': private
  '@aws-sdk/core@3.816.0':
    '@aws-sdk/core': private
  '@aws-sdk/credential-provider-cognito-identity@3.817.0':
    '@aws-sdk/credential-provider-cognito-identity': private
  '@aws-sdk/credential-provider-env@3.816.0':
    '@aws-sdk/credential-provider-env': private
  '@aws-sdk/credential-provider-http@3.816.0':
    '@aws-sdk/credential-provider-http': private
  '@aws-sdk/credential-provider-ini@3.817.0':
    '@aws-sdk/credential-provider-ini': private
  '@aws-sdk/credential-provider-node@3.817.0':
    '@aws-sdk/credential-provider-node': private
  '@aws-sdk/credential-provider-process@3.816.0':
    '@aws-sdk/credential-provider-process': private
  '@aws-sdk/credential-provider-sso@3.817.0':
    '@aws-sdk/credential-provider-sso': private
  '@aws-sdk/credential-provider-web-identity@3.817.0':
    '@aws-sdk/credential-provider-web-identity': private
  '@aws-sdk/credential-providers@3.817.0':
    '@aws-sdk/credential-providers': private
  '@aws-sdk/eventstream-handler-node@3.804.0':
    '@aws-sdk/eventstream-handler-node': private
  '@aws-sdk/middleware-eventstream@3.804.0':
    '@aws-sdk/middleware-eventstream': private
  '@aws-sdk/middleware-host-header@3.804.0':
    '@aws-sdk/middleware-host-header': private
  '@aws-sdk/middleware-logger@3.804.0':
    '@aws-sdk/middleware-logger': private
  '@aws-sdk/middleware-recursion-detection@3.804.0':
    '@aws-sdk/middleware-recursion-detection': private
  '@aws-sdk/middleware-user-agent@3.816.0':
    '@aws-sdk/middleware-user-agent': private
  '@aws-sdk/nested-clients@3.817.0':
    '@aws-sdk/nested-clients': private
  '@aws-sdk/region-config-resolver@3.808.0':
    '@aws-sdk/region-config-resolver': private
  '@aws-sdk/token-providers@3.817.0':
    '@aws-sdk/token-providers': private
  '@aws-sdk/types@3.804.0':
    '@aws-sdk/types': private
  '@aws-sdk/util-endpoints@3.808.0':
    '@aws-sdk/util-endpoints': private
  '@aws-sdk/util-locate-window@3.804.0':
    '@aws-sdk/util-locate-window': private
  '@aws-sdk/util-user-agent-browser@3.804.0':
    '@aws-sdk/util-user-agent-browser': private
  '@aws-sdk/util-user-agent-node@3.816.0':
    '@aws-sdk/util-user-agent-node': private
  '@aws-sdk/util-utf8-browser@3.259.0':
    '@aws-sdk/util-utf8-browser': private
  '@azure/abort-controller@2.1.2':
    '@azure/abort-controller': private
  '@azure/core-auth@1.9.0':
    '@azure/core-auth': private
  '@azure/core-client@1.9.4':
    '@azure/core-client': private
  '@azure/core-rest-pipeline@1.20.0':
    '@azure/core-rest-pipeline': private
  '@azure/core-tracing@1.2.0':
    '@azure/core-tracing': private
  '@azure/core-util@1.12.0':
    '@azure/core-util': private
  '@azure/identity@4.9.1':
    '@azure/identity': private
  '@azure/logger@1.2.0':
    '@azure/logger': private
  '@azure/msal-browser@4.12.0':
    '@azure/msal-browser': private
  '@azure/msal-common@15.6.0':
    '@azure/msal-common': private
  '@azure/msal-node@3.5.3':
    '@azure/msal-node': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.27.2':
    '@babel/compat-data': private
  '@babel/core@7.27.1':
    '@babel/core': private
  '@babel/generator@7.27.1':
    '@babel/generator': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    '@babel/helper-module-transforms': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.1':
    '@babel/helpers': private
  '@babel/parser@7.27.2':
    '@babel/parser': private
  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-async-generators': private
  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-bigint': private
  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.27.1)':
    '@babel/plugin-syntax-class-properties': private
  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-class-static-block': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-json-strings': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-logical-assignment-operators': private
  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-nullish-coalescing-operator': private
  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.27.1)':
    '@babel/plugin-syntax-numeric-separator': private
  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-object-rest-spread': private
  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-optional-catch-binding': private
  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.27.1)':
    '@babel/plugin-syntax-optional-chaining': private
  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-private-property-in-object': private
  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.27.1)':
    '@babel/plugin-syntax-top-level-await': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/runtime@7.27.1':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.27.1':
    '@babel/traverse': private
  '@babel/types@7.27.1':
    '@babel/types': private
  '@bcoe/v8-coverage@0.2.3':
    '@bcoe/v8-coverage': private
  '@braintree/sanitize-url@7.1.1':
    '@braintree/sanitize-url': private
  '@changesets/apply-release-plan@7.0.12':
    '@changesets/apply-release-plan': private
  '@changesets/assemble-release-plan@6.0.8':
    '@changesets/assemble-release-plan': private
  '@changesets/changelog-git@0.2.1':
    '@changesets/changelog-git': private
  '@changesets/config@3.1.1':
    '@changesets/config': private
  '@changesets/errors@0.2.0':
    '@changesets/errors': private
  '@changesets/get-dependents-graph@2.1.3':
    '@changesets/get-dependents-graph': private
  '@changesets/get-release-plan@4.0.12':
    '@changesets/get-release-plan': private
  '@changesets/get-version-range-type@0.4.0':
    '@changesets/get-version-range-type': private
  '@changesets/git@3.0.4':
    '@changesets/git': private
  '@changesets/logger@0.1.1':
    '@changesets/logger': private
  '@changesets/parse@0.4.1':
    '@changesets/parse': private
  '@changesets/pre@2.0.2':
    '@changesets/pre': private
  '@changesets/read@0.6.5':
    '@changesets/read': private
  '@changesets/should-skip-package@0.1.2':
    '@changesets/should-skip-package': private
  '@changesets/types@6.1.0':
    '@changesets/types': private
  '@changesets/write@0.4.0':
    '@changesets/write': private
  '@chevrotain/cst-dts-gen@11.0.3':
    '@chevrotain/cst-dts-gen': private
  '@chevrotain/gast@11.0.3':
    '@chevrotain/gast': private
  '@chevrotain/regexp-to-ast@11.0.3':
    '@chevrotain/regexp-to-ast': private
  '@chevrotain/types@11.0.3':
    '@chevrotain/types': private
  '@chevrotain/utils@11.0.3':
    '@chevrotain/utils': private
  '@ecies/ciphers@0.2.3(@noble/ciphers@1.3.0)':
    '@ecies/ciphers': private
  '@emotion/is-prop-valid@1.2.2':
    '@emotion/is-prop-valid': private
  '@emotion/memoize@0.8.1':
    '@emotion/memoize': private
  '@emotion/unitless@0.8.1':
    '@emotion/unitless': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.27.0(jiti@2.4.2))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.20.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.2.2':
    '@eslint/config-helpers': private
  '@eslint/core@0.14.0':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/js@9.27.0':
    '@eslint/js': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.1':
    '@eslint/plugin-kit': private
  '@fastify/busboy@2.1.1':
    '@fastify/busboy': private
  '@floating-ui/core@1.7.0':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.0':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@floating-ui/react-dom': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@google/genai@0.13.0':
    '@google/genai': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@istanbuljs/load-nyc-config@1.1.0':
    '@istanbuljs/load-nyc-config': private
  '@istanbuljs/schema@0.1.3':
    '@istanbuljs/schema': private
  '@jest/console@29.7.0':
    '@jest/console': private
  '@jest/core@29.7.0(babel-plugin-macros@3.1.0)':
    '@jest/core': private
  '@jest/environment@29.7.0':
    '@jest/environment': private
  '@jest/expect-utils@29.7.0':
    '@jest/expect-utils': private
  '@jest/expect@29.7.0':
    '@jest/expect': private
  '@jest/fake-timers@29.7.0':
    '@jest/fake-timers': private
  '@jest/globals@29.7.0':
    '@jest/globals': private
  '@jest/reporters@29.7.0':
    '@jest/reporters': private
  '@jest/schemas@29.6.3':
    '@jest/schemas': private
  '@jest/source-map@29.6.3':
    '@jest/source-map': private
  '@jest/test-result@29.7.0':
    '@jest/test-result': private
  '@jest/test-sequencer@29.7.0':
    '@jest/test-sequencer': private
  '@jest/transform@29.7.0':
    '@jest/transform': private
  '@jest/types@29.6.3':
    '@jest/types': private
  '@joshwooding/vite-plugin-react-docgen-typescript@0.5.0(typescript@5.8.3)(vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@joshwooding/vite-plugin-react-docgen-typescript': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@kwsites/file-exists@1.1.1':
    '@kwsites/file-exists': private
  '@kwsites/promise-deferred@1.1.1':
    '@kwsites/promise-deferred': private
  '@manypkg/find-root@1.1.0':
    '@manypkg/find-root': private
  '@manypkg/get-packages@1.1.3':
    '@manypkg/get-packages': private
  '@mapbox/hast-util-table-cell-style@0.2.1':
    '@mapbox/hast-util-table-cell-style': private
  '@mdx-js/react@3.1.0(@types/react@18.3.21)(react@18.3.1)':
    '@mdx-js/react': private
  '@mermaid-js/parser@0.4.0':
    '@mermaid-js/parser': private
  '@microsoft/fast-element@1.14.0':
    '@microsoft/fast-element': private
  '@microsoft/fast-foundation@2.50.0':
    '@microsoft/fast-foundation': private
  '@microsoft/fast-react-wrapper@0.3.25(react@18.3.1)':
    '@microsoft/fast-react-wrapper': private
  '@microsoft/fast-web-utilities@5.4.1':
    '@microsoft/fast-web-utilities': private
  '@mistralai/mistralai@1.6.1(zod@3.24.4)':
    '@mistralai/mistralai': private
  '@mixmark-io/domino@2.2.0':
    '@mixmark-io/domino': private
  '@modelcontextprotocol/sdk@1.12.0':
    '@modelcontextprotocol/sdk': private
  '@mswjs/interceptors@0.38.6':
    '@mswjs/interceptors': private
  '@next/eslint-plugin-next@15.3.2':
    '@next/eslint-plugin-next': private
  '@noble/ciphers@1.3.0':
    '@noble/ciphers': private
  '@noble/curves@1.9.1':
    '@noble/curves': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@open-draft/deferred-promise@2.2.0':
    '@open-draft/deferred-promise': private
  '@open-draft/logger@0.3.0':
    '@open-draft/logger': private
  '@open-draft/until@2.1.0':
    '@open-draft/until': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@puppeteer/browsers@2.10.5':
    '@puppeteer/browsers': private
  '@qdrant/js-client-rest@1.14.0(typescript@5.8.3)':
    '@qdrant/js-client-rest': private
  '@qdrant/openapi-typescript-fetch@1.2.6':
    '@qdrant/openapi-typescript-fetch': private
  '@radix-ui/number@1.1.1':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.2':
    '@radix-ui/primitive': private
  '@radix-ui/react-alert-dialog@1.1.13(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-alert-dialog': private
  '@radix-ui/react-arrow@1.1.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-checkbox@1.3.1(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-checkbox': private
  '@radix-ui/react-collapsible@1.1.10(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collapsible': private
  '@radix-ui/react-collection@1.1.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-context': private
  '@radix-ui/react-dialog@1.1.13(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dialog': private
  '@radix-ui/react-direction@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-dropdown-menu@2.1.14(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-dropdown-menu': private
  '@radix-ui/react-focus-guards@1.1.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-icons@1.3.2(react@18.3.1)':
    '@radix-ui/react-icons': private
  '@radix-ui/react-id@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.14(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popover@1.1.13(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popover': private
  '@radix-ui/react-popper@1.2.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.8(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.4(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.1.2(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-progress@1.1.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-progress': private
  '@radix-ui/react-roving-focus@1.1.9(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-select@2.2.4(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-select': private
  '@radix-ui/react-separator@1.1.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-separator': private
  '@radix-ui/react-slider@1.3.4(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-slider': private
  '@radix-ui/react-slot@1.2.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-slot': private
  '@radix-ui/react-tooltip@1.2.6(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-tooltip': private
  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-effect-event@0.0.2(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-effect-event': private
  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.1(@types/react@18.3.21)(react@18.3.1)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.2.2(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.1':
    '@radix-ui/rect': private
  '@rollup/pluginutils@5.1.4(rollup@4.40.2)':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.40.2':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.40.2':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.40.2':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.40.2':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.40.2':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.40.2':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.40.2':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.40.2':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.40.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.40.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.40.2':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.40.2':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.40.2':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.40.2':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.40.2':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.40.2':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.40.2':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.40.2':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.40.2':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.40.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sevinf/maybe@0.5.0':
    '@sevinf/maybe': private
  '@shikijs/core@3.4.1':
    '@shikijs/core': private
  '@shikijs/engine-javascript@3.4.1':
    '@shikijs/engine-javascript': private
  '@shikijs/engine-oniguruma@3.4.1':
    '@shikijs/engine-oniguruma': private
  '@shikijs/langs@3.4.1':
    '@shikijs/langs': private
  '@shikijs/themes@3.4.1':
    '@shikijs/themes': private
  '@shikijs/types@3.4.1':
    '@shikijs/types': private
  '@shikijs/vscode-textmate@10.0.2':
    '@shikijs/vscode-textmate': private
  '@sinclair/typebox@0.27.8':
    '@sinclair/typebox': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@sinonjs/commons@3.0.1':
    '@sinonjs/commons': private
  '@sinonjs/fake-timers@10.3.0':
    '@sinonjs/fake-timers': private
  '@smithy/abort-controller@4.0.3':
    '@smithy/abort-controller': private
  '@smithy/config-resolver@4.1.3':
    '@smithy/config-resolver': private
  '@smithy/core@3.4.0':
    '@smithy/core': private
  '@smithy/credential-provider-imds@4.0.5':
    '@smithy/credential-provider-imds': private
  '@smithy/eventstream-codec@4.0.3':
    '@smithy/eventstream-codec': private
  '@smithy/eventstream-serde-browser@4.0.3':
    '@smithy/eventstream-serde-browser': private
  '@smithy/eventstream-serde-config-resolver@4.1.1':
    '@smithy/eventstream-serde-config-resolver': private
  '@smithy/eventstream-serde-node@2.2.0':
    '@smithy/eventstream-serde-node': private
  '@smithy/eventstream-serde-universal@4.0.3':
    '@smithy/eventstream-serde-universal': private
  '@smithy/fetch-http-handler@2.5.0':
    '@smithy/fetch-http-handler': private
  '@smithy/hash-node@4.0.3':
    '@smithy/hash-node': private
  '@smithy/invalid-dependency@4.0.3':
    '@smithy/invalid-dependency': private
  '@smithy/is-array-buffer@3.0.0':
    '@smithy/is-array-buffer': private
  '@smithy/middleware-content-length@4.0.3':
    '@smithy/middleware-content-length': private
  '@smithy/middleware-endpoint@4.1.7':
    '@smithy/middleware-endpoint': private
  '@smithy/middleware-retry@4.1.8':
    '@smithy/middleware-retry': private
  '@smithy/middleware-serde@4.0.6':
    '@smithy/middleware-serde': private
  '@smithy/middleware-stack@4.0.3':
    '@smithy/middleware-stack': private
  '@smithy/node-config-provider@4.1.2':
    '@smithy/node-config-provider': private
  '@smithy/node-http-handler@4.0.5':
    '@smithy/node-http-handler': private
  '@smithy/property-provider@4.0.3':
    '@smithy/property-provider': private
  '@smithy/protocol-http@3.3.0':
    '@smithy/protocol-http': private
  '@smithy/querystring-builder@2.2.0':
    '@smithy/querystring-builder': private
  '@smithy/querystring-parser@4.0.3':
    '@smithy/querystring-parser': private
  '@smithy/service-error-classification@4.0.4':
    '@smithy/service-error-classification': private
  '@smithy/shared-ini-file-loader@4.0.3':
    '@smithy/shared-ini-file-loader': private
  '@smithy/signature-v4@3.1.2':
    '@smithy/signature-v4': private
  '@smithy/smithy-client@2.5.1':
    '@smithy/smithy-client': private
  '@smithy/types@2.12.0':
    '@smithy/types': private
  '@smithy/url-parser@4.0.3':
    '@smithy/url-parser': private
  '@smithy/util-base64@2.3.0':
    '@smithy/util-base64': private
  '@smithy/util-body-length-browser@4.0.0':
    '@smithy/util-body-length-browser': private
  '@smithy/util-body-length-node@4.0.0':
    '@smithy/util-body-length-node': private
  '@smithy/util-buffer-from@4.0.0':
    '@smithy/util-buffer-from': private
  '@smithy/util-config-provider@4.0.0':
    '@smithy/util-config-provider': private
  '@smithy/util-defaults-mode-browser@4.0.15':
    '@smithy/util-defaults-mode-browser': private
  '@smithy/util-defaults-mode-node@4.0.15':
    '@smithy/util-defaults-mode-node': private
  '@smithy/util-endpoints@3.0.5':
    '@smithy/util-endpoints': private
  '@smithy/util-hex-encoding@3.0.0':
    '@smithy/util-hex-encoding': private
  '@smithy/util-middleware@4.0.3':
    '@smithy/util-middleware': private
  '@smithy/util-retry@4.0.4':
    '@smithy/util-retry': private
  '@smithy/util-stream@4.2.1':
    '@smithy/util-stream': private
  '@smithy/util-uri-escape@3.0.0':
    '@smithy/util-uri-escape': private
  '@smithy/util-utf8@4.0.0':
    '@smithy/util-utf8': private
  '@storybook/addon-actions@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-actions': private
  '@storybook/addon-backgrounds@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-backgrounds': private
  '@storybook/addon-controls@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-controls': private
  '@storybook/addon-docs@8.6.12(@types/react@18.3.21)(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-docs': private
  '@storybook/addon-essentials@8.6.12(@types/react@18.3.21)(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-essentials': private
  '@storybook/addon-highlight@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-highlight': private
  '@storybook/addon-measure@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-measure': private
  '@storybook/addon-outline@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-outline': private
  '@storybook/addon-toolbars@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-toolbars': private
  '@storybook/addon-viewport@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/addon-viewport': private
  '@storybook/blocks@8.6.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/blocks': private
  '@storybook/builder-vite@8.6.12(storybook@8.6.12(prettier@3.5.3))(vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@storybook/builder-vite': private
  '@storybook/components@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/components': private
  '@storybook/core-events@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/core-events': private
  '@storybook/core@8.6.12(prettier@3.5.3)(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/core': private
  '@storybook/csf-plugin@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/csf-plugin': private
  '@storybook/global@5.0.0':
    '@storybook/global': private
  '@storybook/icons@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@storybook/icons': private
  '@storybook/manager-api@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/manager-api': private
  '@storybook/preview-api@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/preview-api': private
  '@storybook/react-dom-shim@8.6.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/react-dom-shim': private
  '@storybook/react-vite@8.6.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(rollup@4.40.2)(storybook@8.6.12(prettier@3.5.3))(typescript@5.8.3)(vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@storybook/react-vite': private
  '@storybook/react@8.6.12(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(storybook@8.6.12(prettier@3.5.3))(typescript@5.8.3)':
    '@storybook/react': private
  '@storybook/theming@8.6.12(storybook@8.6.12(prettier@3.5.3))':
    '@storybook/theming': private
  '@tailwindcss/node@4.1.6':
    '@tailwindcss/node': private
  '@tailwindcss/oxide-android-arm64@4.1.6':
    '@tailwindcss/oxide-android-arm64': private
  '@tailwindcss/oxide-darwin-arm64@4.1.6':
    '@tailwindcss/oxide-darwin-arm64': private
  '@tailwindcss/oxide-darwin-x64@4.1.6':
    '@tailwindcss/oxide-darwin-x64': private
  '@tailwindcss/oxide-freebsd-x64@4.1.6':
    '@tailwindcss/oxide-freebsd-x64': private
  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.6':
    '@tailwindcss/oxide-linux-arm-gnueabihf': private
  '@tailwindcss/oxide-linux-arm64-gnu@4.1.6':
    '@tailwindcss/oxide-linux-arm64-gnu': private
  '@tailwindcss/oxide-linux-arm64-musl@4.1.6':
    '@tailwindcss/oxide-linux-arm64-musl': private
  '@tailwindcss/oxide-linux-x64-gnu@4.1.6':
    '@tailwindcss/oxide-linux-x64-gnu': private
  '@tailwindcss/oxide-linux-x64-musl@4.1.6':
    '@tailwindcss/oxide-linux-x64-musl': private
  '@tailwindcss/oxide-wasm32-wasi@4.1.6':
    '@tailwindcss/oxide-wasm32-wasi': private
  '@tailwindcss/oxide-win32-arm64-msvc@4.1.6':
    '@tailwindcss/oxide-win32-arm64-msvc': private
  '@tailwindcss/oxide-win32-x64-msvc@4.1.6':
    '@tailwindcss/oxide-win32-x64-msvc': private
  '@tailwindcss/oxide@4.1.6':
    '@tailwindcss/oxide': private
  '@tailwindcss/vite@4.1.6(vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@tailwindcss/vite': private
  '@tanstack/query-core@5.76.0':
    '@tanstack/query-core': private
  '@tanstack/react-query@5.76.1(react@18.3.1)':
    '@tanstack/react-query': private
  '@testing-library/dom@10.4.0':
    '@testing-library/dom': private
  '@testing-library/jest-dom@6.6.3':
    '@testing-library/jest-dom': private
  '@testing-library/react@16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@testing-library/react': private
  '@testing-library/user-event@14.6.1(@testing-library/dom@10.4.0)':
    '@testing-library/user-event': private
  '@tootallnate/once@2.0.0':
    '@tootallnate/once': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@types/aria-query@5.0.4':
    '@types/aria-query': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/clone-deep@4.0.4':
    '@types/clone-deep': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-axis@3.0.6':
    '@types/d3-axis': private
  '@types/d3-brush@3.0.6':
    '@types/d3-brush': private
  '@types/d3-chord@3.0.6':
    '@types/d3-chord': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-contour@3.0.6':
    '@types/d3-contour': private
  '@types/d3-delaunay@6.0.4':
    '@types/d3-delaunay': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-polygon@3.0.2':
    '@types/d3-polygon': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time-format@4.0.3':
    '@types/d3-time-format': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/d3@7.4.3':
    '@types/d3': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/diff@5.2.3':
    '@types/diff': private
  '@types/doctrine@0.0.9':
    '@types/doctrine': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/glob@8.1.0':
    '@types/glob': private
  '@types/graceful-fs@4.1.9':
    '@types/graceful-fs': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/istanbul-lib-coverage@2.0.6':
    '@types/istanbul-lib-coverage': private
  '@types/istanbul-lib-report@3.0.3':
    '@types/istanbul-lib-report': private
  '@types/istanbul-reports@3.0.4':
    '@types/istanbul-reports': private
  '@types/jest@29.5.14':
    '@types/jest': private
  '@types/js-cookie@2.2.7':
    '@types/js-cookie': private
  '@types/jsdom@20.0.1':
    '@types/jsdom': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/lodash.debounce@4.0.9':
    '@types/lodash.debounce': private
  '@types/lodash@4.17.17':
    '@types/lodash': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdx@2.0.13':
    '@types/mdx': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/mocha@10.0.10':
    '@types/mocha': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-cache@4.2.5':
    '@types/node-cache': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/node-ipc@9.2.3':
    '@types/node-ipc': private
  '@types/node@22.15.20':
    '@types/node': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/ps-tree@1.1.6':
    '@types/ps-tree': private
  '@types/react-dom@18.3.7(@types/react@18.3.21)':
    '@types/react-dom': private
  '@types/react@18.3.21':
    '@types/react': private
  '@types/resolve@1.20.6':
    '@types/resolve': private
  '@types/shell-quote@1.7.5':
    '@types/shell-quote': private
  '@types/stack-utils@2.0.3':
    '@types/stack-utils': private
  '@types/string-similarity@4.0.2':
    '@types/string-similarity': private
  '@types/stylis@4.2.5':
    '@types/stylis': private
  '@types/testing-library__jest-dom@5.14.9':
    '@types/testing-library__jest-dom': private
  '@types/tmp@0.2.6':
    '@types/tmp': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/turndown@5.0.5':
    '@types/turndown': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/vscode-webview@1.57.5':
    '@types/vscode-webview': private
  '@types/vscode@1.100.0':
    '@types/vscode': private
  '@types/yargs-parser@21.0.3':
    '@types/yargs-parser': private
  '@types/yargs@17.0.33':
    '@types/yargs': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  '@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/scope-manager@8.32.1':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.32.1':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.32.1(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.32.1':
    '@typescript-eslint/visitor-keys': private
  '@typespec/ts-http-runtime@0.2.2':
    '@typespec/ts-http-runtime': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitejs/plugin-react@4.4.1(vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@vitejs/plugin-react': private
  '@vitest/expect@3.1.3':
    '@vitest/expect': private
  '@vitest/mocker@3.1.3(vite@6.3.5(@types/node@20.17.50)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.1.3':
    '@vitest/pretty-format': private
  '@vitest/runner@3.1.3':
    '@vitest/runner': private
  '@vitest/snapshot@3.1.3':
    '@vitest/snapshot': private
  '@vitest/spy@3.1.3':
    '@vitest/spy': private
  '@vitest/utils@3.1.3':
    '@vitest/utils': private
  '@vscode/codicons@0.0.36':
    '@vscode/codicons': private
  '@vscode/test-cli@0.0.11':
    '@vscode/test-cli': private
  '@vscode/test-electron@2.5.2':
    '@vscode/test-electron': private
  '@vscode/vsce-sign-alpine-arm64@2.0.2':
    '@vscode/vsce-sign-alpine-arm64': private
  '@vscode/vsce-sign-alpine-x64@2.0.2':
    '@vscode/vsce-sign-alpine-x64': private
  '@vscode/vsce-sign-darwin-arm64@2.0.2':
    '@vscode/vsce-sign-darwin-arm64': private
  '@vscode/vsce-sign-darwin-x64@2.0.2':
    '@vscode/vsce-sign-darwin-x64': private
  '@vscode/vsce-sign-linux-arm64@2.0.2':
    '@vscode/vsce-sign-linux-arm64': private
  '@vscode/vsce-sign-linux-arm@2.0.2':
    '@vscode/vsce-sign-linux-arm': private
  '@vscode/vsce-sign-linux-x64@2.0.2':
    '@vscode/vsce-sign-linux-x64': private
  '@vscode/vsce-sign-win32-arm64@2.0.2':
    '@vscode/vsce-sign-win32-arm64': private
  '@vscode/vsce-sign-win32-x64@2.0.2':
    '@vscode/vsce-sign-win32-x64': private
  '@vscode/vsce-sign@2.0.5':
    '@vscode/vsce-sign': private
  '@vscode/webview-ui-toolkit@1.4.0(react@18.3.1)':
    '@vscode/webview-ui-toolkit': private
  '@xmldom/xmldom@0.8.10':
    '@xmldom/xmldom': private
  '@xobotyi/scrollbar-width@1.9.5':
    '@xobotyi/scrollbar-width': private
  abab@2.0.6:
    abab: private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@2.0.0:
    accepts: private
  acorn-globals@7.0.1:
    acorn-globals: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  apps/vscode-e2e:
    '@qapt-coder/vscode-e2e': private
  apps/vscode-nightly:
    '@qapt-coder/vscode-nightly': private
  aproba@2.0.0:
    aproba: private
  argparse@1.0.10:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assertion-error@2.0.1:
    assertion-error: private
  ast-types@0.16.1:
    ast-types: private
  async-function@1.0.0:
    async-function: private
  async-mutex@0.5.0:
    async-mutex: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axios@1.9.0:
    axios: private
  azure-devops-node-api@12.5.0:
    azure-devops-node-api: private
  b4a@1.6.7:
    b4a: private
  babel-jest@29.7.0(@babel/core@7.27.1):
    babel-jest: private
  babel-plugin-istanbul@6.1.1:
    babel-plugin-istanbul: private
  babel-plugin-jest-hoist@29.6.3:
    babel-plugin-jest-hoist: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  babel-preset-current-node-syntax@1.1.0(@babel/core@7.27.1):
    babel-preset-current-node-syntax: private
  babel-preset-jest@29.6.3(@babel/core@7.27.1):
    babel-preset-jest: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  bare-events@2.5.4:
    bare-events: private
  bare-fs@4.1.5:
    bare-fs: private
  bare-os@3.6.1:
    bare-os: private
  bare-path@3.0.0:
    bare-path: private
  bare-stream@2.6.5(bare-events@2.5.4):
    bare-stream: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  better-opn@3.0.2:
    better-opn: private
  better-path-resolve@1.0.0:
    better-path-resolve: private
  bignumber.js@9.3.0:
    bignumber.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@4.1.0:
    bl: private
  bluebird@3.4.7:
    bluebird: private
  body-parser@2.2.0:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  bowser@2.11.0:
    bowser: private
  brace-expansion@2.0.1:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browser-assert@1.2.1:
    browser-assert: private
  browser-stdout@1.3.1:
    browser-stdout: private
  browserslist@4.24.5:
    browserslist: private
  bs-logger@0.2.6:
    bs-logger: private
  bser@2.1.1:
    bser: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@5.7.1:
    buffer: private
  bundle-name@4.1.0:
    bundle-name: private
  bundle-require@5.1.0(esbuild@0.25.5):
    bundle-require: private
  bytes@3.1.2:
    bytes: private
  c8@9.1.0:
    c8: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@6.3.0:
    camelcase: private
  camelize@1.0.1:
    camelize: private
  caniuse-lite@1.0.30001718:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chai@5.2.0:
    chai: private
  chalk@3.0.0:
    chalk: private
  char-regex@1.0.2:
    char-regex: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chardet@0.7.0:
    chardet: private
  check-error@2.1.1:
    check-error: private
  cheerio-select@2.1.0:
    cheerio-select: private
  cheerio@1.0.0:
    cheerio: private
  chevrotain-allstar@0.3.1(chevrotain@11.0.3):
    chevrotain-allstar: private
  chevrotain@11.0.3:
    chevrotain: private
  chokidar@4.0.3:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  chromium-bidi@0.11.0(devtools-protocol@0.0.1367902):
    chromium-bidi: private
  ci-info@3.9.0:
    ci-info: private
  cjs-module-lexer@1.4.3:
    cjs-module-lexer: private
  class-variance-authority@0.7.1:
    class-variance-authority: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cliui@8.0.1:
    cliui: private
  clone-deep@4.0.1:
    clone-deep: private
  clone@2.1.2:
    clone: private
  clsx@2.1.1:
    clsx: private
  cmdk@1.1.1(@types/react-dom@18.3.7(@types/react@18.3.21))(@types/react@18.3.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    cmdk: private
  co@4.6.0:
    co: private
  cockatiel@3.2.1:
    cockatiel: private
  collect-v8-coverage@1.0.2:
    collect-v8-coverage: private
  color-convert@1.9.3:
    color-convert: private
  color-name@1.1.3:
    color-name: private
  color-support@1.1.3:
    color-support: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@11.1.0:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  console-control-strings@1.1.0:
    console-control-strings: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  copyfiles@2.4.1:
    copyfiles: private
  core-js@3.42.0:
    core-js: private
  core-util-is@1.0.3:
    core-util-is: private
  cors@2.8.5:
    cors: private
  cose-base@1.0.3:
    cose-base: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  create-jest@29.7.0(@types/node@18.19.100)(babel-plugin-macros@3.1.0):
    create-jest: private
  cross-fetch@4.0.0:
    cross-fetch: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-color-keywords@1.0.0:
    css-color-keywords: private
  css-in-js-utils@3.1.0:
    css-in-js-utils: private
  css-select@5.1.0:
    css-select: private
  css-to-react-native@3.2.0:
    css-to-react-native: private
  css-tree@1.1.3:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  css.escape@1.5.1:
    css.escape: private
  cssom@0.5.0:
    cssom: private
  cssstyle@2.3.0:
    cssstyle: private
  csstype@3.1.3:
    csstype: private
  cytoscape-cose-bilkent@4.1.0(cytoscape@3.32.0):
    cytoscape-cose-bilkent: private
  cytoscape-fcose@2.2.0(cytoscape@3.32.0):
    cytoscape-fcose: private
  cytoscape@3.32.0:
    cytoscape: private
  d3-array@2.12.1:
    d3-array: private
  d3-axis@3.0.0:
    d3-axis: private
  d3-brush@3.0.0:
    d3-brush: private
  d3-chord@3.0.1:
    d3-chord: private
  d3-color@3.1.0:
    d3-color: private
  d3-contour@4.0.2:
    d3-contour: private
  d3-delaunay@6.0.4:
    d3-delaunay: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-polygon@3.0.1:
    d3-polygon: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-sankey@0.12.3:
    d3-sankey: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-shape@1.3.7:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  d3@7.9.0:
    d3: private
  dagre-d3-es@7.0.11:
    dagre-d3-es: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  data-urls@3.0.2:
    data-urls: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  date-fns@4.1.0:
    date-fns: private
  dayjs@1.11.13:
    dayjs: private
  debounce@2.2.0:
    debounce: private
  debug@4.4.1(supports-color@8.1.1):
    debug: private
  decamelize@4.0.0:
    decamelize: private
  decimal.js@10.5.0:
    decimal.js: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  decompress-response@6.0.0:
    decompress-response: private
  dedent@1.6.0(babel-plugin-macros@3.1.0):
    dedent: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  default-shell@2.2.0:
    default-shell: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  degenerator@5.0.1:
    degenerator: private
  delaunator@5.0.1:
    delaunator: private
  delay@6.0.0:
    delay: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  detect-indent@6.1.0:
    detect-indent: private
  detect-libc@2.0.4:
    detect-libc: private
  detect-newline@3.1.0:
    detect-newline: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  devtools-protocol@0.0.1367902:
    devtools-protocol: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  diff-sequences@27.5.1:
    diff-sequences: private
  diff@5.2.0:
    diff: private
  dingbat-to-unicode@1.0.1:
    dingbat-to-unicode: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  dom-accessibility-api@0.6.3:
    dom-accessibility-api: private
  dom-serializer@2.0.0:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domexception@4.0.0:
    domexception: private
  domhandler@5.0.3:
    domhandler: private
  dompurify@3.2.5:
    dompurify: private
  domutils@3.2.2:
    domutils: private
  dotenv@16.5.0:
    dotenv: private
  duck@0.1.12:
    duck: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer@0.1.2:
    duplexer: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  easy-stack@1.0.1:
    easy-stack: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  eciesjs@0.4.15:
    eciesjs: private
  ee-first@1.1.1:
    ee-first: private
  eight-colors@1.3.1:
    eight-colors: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.152:
    electron-to-chromium: private
  emittery@0.13.1:
    emittery: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  encoding-sniffer@0.2.0:
    encoding-sniffer: private
  end-of-stream@1.4.4:
    end-of-stream: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  enquirer@2.4.1:
    enquirer: private
  entities@4.5.0:
    entities: private
  environment@1.1.0:
    environment: private
  error-ex@1.3.2:
    error-ex: private
  error-stack-parser@2.1.4:
    error-stack-parser: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild-register@3.6.0(esbuild@0.25.5):
    esbuild-register: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-prettier@10.1.5(eslint@9.27.0(jiti@2.4.2)):
    eslint-config-prettier: private
  eslint-plugin-only-warn@1.1.0:
    eslint-plugin-only-warn: private
  eslint-plugin-react-hooks@5.2.0(eslint@9.27.0(jiti@2.4.2)):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.5(eslint@9.27.0(jiti@2.4.2)):
    eslint-plugin-react: private
  eslint-plugin-turbo@2.5.3(eslint@9.27.0(jiti@2.4.2))(turbo@2.5.4):
    eslint-plugin-turbo: private
  eslint-scope@8.3.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: private
  espree@10.3.0:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-pubsub@5.0.3:
    event-pubsub: private
  event-stream@3.3.4:
    event-stream: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  eventsource-parser@3.0.2:
    eventsource-parser: private
  eventsource@3.0.7:
    eventsource: private
  execa@9.5.3:
    execa: private
  exenv-es6@1.1.1:
    exenv-es6: private
  exit@0.1.2:
    exit: private
  expand-template@2.0.3:
    expand-template: private
  expect-type@1.2.1:
    expect-type: private
  expect@29.7.0:
    expect: private
  express-rate-limit@7.5.0(express@5.1.0):
    express-rate-limit: private
  express@5.1.0:
    express: private
  exsolve@1.0.5:
    exsolve: private
  extend@3.0.2:
    extend: private
  extendable-error@0.1.7:
    extendable-error: private
  external-editor@3.1.0:
    external-editor: private
  extract-zip@2.0.1:
    extract-zip: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-fifo@1.3.2:
    fast-fifo: private
  fast-glob@3.3.1:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-shallow-equal@1.0.0:
    fast-shallow-equal: private
  fast-xml-parser@4.5.3:
    fast-xml-parser: private
  fastest-levenshtein@1.0.16:
    fastest-levenshtein: private
  fastest-stable-stringify@2.0.2:
    fastest-stable-stringify: private
  fastq@1.19.1:
    fastq: private
  fb-watchman@2.0.2:
    fb-watchman: private
  fd-package-json@1.2.0:
    fd-package-json: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fdir@6.4.5(picomatch@4.0.2):
    fdir: private
  fflate@0.4.8:
    fflate: private
  figures@6.1.0:
    figures: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  fix-dts-default-cjs-exports@1.0.1:
    fix-dts-default-cjs-exports: private
  flat-cache@4.0.1:
    flat-cache: private
  flat@5.0.2:
    flat: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.2:
    form-data: private
  formatly@0.2.3:
    formatly: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  from@0.1.7:
    from: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@7.0.1:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  fzf@0.5.2:
    fzf: private
  gauge@5.0.2:
    gauge: private
  gaxios@6.7.1:
    gaxios: private
  gcp-metadata@6.1.1:
    gcp-metadata: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-folder-size@5.0.0:
    get-folder-size: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-package-type@0.1.0:
    get-package-type: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@9.0.1:
    get-stream: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  get-uri@6.0.4:
    get-uri: private
  github-from-package@0.0.0:
    github-from-package: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@11.0.2:
    glob: private
  globals@16.1.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  google-auth-library@9.15.1:
    google-auth-library: private
  google-logging-utils@0.0.2:
    google-logging-utils: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gtoken@7.1.0:
    gtoken: private
  hachure-fill@0.5.2:
    hachure-fill: private
  harmony-reflect@1.6.2:
    harmony-reflect: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-unicode@2.0.1:
    has-unicode: private
  hasown@2.0.2:
    hasown: private
  hast-to-hyperscript@9.0.1:
    hast-to-hyperscript: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-to-html@9.0.5:
    hast-util-to-html: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  he@1.2.0:
    he: private
  highlight.js@11.11.1:
    highlight.js: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  howler@2.2.4:
    howler: private
  html-encoding-sniffer@3.0.0:
    html-encoding-sniffer: private
  html-escaper@2.0.2:
    html-escaper: private
  html-parse-stringify@3.0.1:
    html-parse-stringify: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  html-void-elements@3.0.0:
    html-void-elements: private
  htmlparser2@9.1.0:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-id@4.1.1:
    human-id: private
  human-signals@8.0.1:
    human-signals: private
  humanize-ms@1.2.1:
    humanize-ms: private
  hyphenate-style-name@1.1.0:
    hyphenate-style-name: private
  i18next-http-backend@3.0.2:
    i18next-http-backend: private
  i18next@24.2.3(typescript@5.8.3):
    i18next: private
  iconv-lite@0.6.3:
    iconv-lite: private
  identity-obj-proxy@3.0.0:
    identity-obj-proxy: private
  ieee754@1.2.1:
    ieee754: private
  ignore@7.0.4:
    ignore: private
  immediate@3.0.6:
    immediate: private
  import-fresh@3.3.1:
    import-fresh: private
  import-local@3.2.0:
    import-local: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inline-style-parser@0.1.1:
    inline-style-parser: private
  inline-style-prefixer@7.0.1:
    inline-style-prefixer: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@1.0.1:
    internmap: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-arguments@1.2.0:
    is-arguments: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-buffer@2.0.5:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-ci@2.0.0:
    is-ci: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-fn@2.1.0:
    is-generator-fn: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-interactive@2.0.0:
    is-interactive: private
  is-map@2.0.3:
    is-map: private
  is-node-process@1.2.0:
    is-node-process: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-potential-custom-element-name@1.0.1:
    is-potential-custom-element-name: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@4.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-subdir@1.2.0:
    is-subdir: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@1.0.0:
    isarray: private
  isbinaryfile@5.0.4:
    isbinaryfile: private
  isexe@3.1.1:
    isexe: private
  isobject@3.0.1:
    isobject: private
  istanbul-lib-coverage@3.2.2:
    istanbul-lib-coverage: private
  istanbul-lib-instrument@6.0.3:
    istanbul-lib-instrument: private
  istanbul-lib-report@3.0.1:
    istanbul-lib-report: private
  istanbul-lib-source-maps@4.0.1:
    istanbul-lib-source-maps: private
  istanbul-reports@3.1.7:
    istanbul-reports: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@4.1.0:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-changed-files@29.7.0:
    jest-changed-files: private
  jest-circus@29.7.0(babel-plugin-macros@3.1.0):
    jest-circus: private
  jest-cli@29.7.0(@types/node@18.19.100)(babel-plugin-macros@3.1.0):
    jest-cli: private
  jest-config@29.7.0(@types/node@22.15.20)(babel-plugin-macros@3.1.0):
    jest-config: private
  jest-diff@27.5.1:
    jest-diff: private
  jest-docblock@29.7.0:
    jest-docblock: private
  jest-each@29.7.0:
    jest-each: private
  jest-environment-jsdom@29.7.0:
    jest-environment-jsdom: private
  jest-environment-node@29.7.0:
    jest-environment-node: private
  jest-get-type@29.6.3:
    jest-get-type: private
  jest-haste-map@29.7.0:
    jest-haste-map: private
  jest-leak-detector@29.7.0:
    jest-leak-detector: private
  jest-matcher-utils@27.5.1:
    jest-matcher-utils: private
  jest-message-util@29.7.0:
    jest-message-util: private
  jest-mock@29.7.0:
    jest-mock: private
  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    jest-pnp-resolver: private
  jest-regex-util@29.6.3:
    jest-regex-util: private
  jest-resolve-dependencies@29.7.0:
    jest-resolve-dependencies: private
  jest-resolve@29.7.0:
    jest-resolve: private
  jest-runner@29.7.0:
    jest-runner: private
  jest-runtime@29.7.0:
    jest-runtime: private
  jest-simple-dot-reporter@1.0.5:
    jest-simple-dot-reporter: private
  jest-snapshot@29.7.0:
    jest-snapshot: private
  jest-util@29.7.0:
    jest-util: private
  jest-validate@29.7.0:
    jest-validate: private
  jest-watcher@29.7.0:
    jest-watcher: private
  jest-worker@29.7.0:
    jest-worker: private
  jest@29.7.0(@types/node@20.17.50)(babel-plugin-macros@3.1.0):
    jest: private
  jiti@2.4.2:
    jiti: private
  joycon@3.1.1:
    joycon: private
  js-cookie@2.2.1:
    js-cookie: private
  js-message@1.0.7:
    js-message: private
  js-queue@2.0.2:
    js-queue: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsdoc-type-pratt-parser@4.1.0:
    jsdoc-type-pratt-parser: private
  jsdom@20.0.3:
    jsdom: private
  jsesc@3.1.0:
    jsesc: private
  json-bigint@1.0.0:
    json-bigint: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@4.0.0:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonc-parser@3.3.1:
    jsonc-parser: private
  jsonfile@4.0.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  jszip@3.10.1:
    jszip: private
  jwa@2.0.1:
    jwa: private
  jws@4.0.0:
    jws: private
  katex@0.16.22:
    katex: private
  keytar@7.9.0:
    keytar: private
  keyv@4.5.4:
    keyv: private
  khroma@2.1.0:
    khroma: private
  kind-of@6.0.3:
    kind-of: private
  kleur@3.0.3:
    kleur: private
  knuth-shuffle-seeded@1.0.6:
    knuth-shuffle-seeded: private
  kolorist@1.8.0:
    kolorist: private
  langium@3.3.1:
    langium: private
  layout-base@1.0.2:
    layout-base: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lie@3.3.0:
    lie: private
  lightningcss-darwin-arm64@1.29.2:
    lightningcss-darwin-arm64: private
  lightningcss-darwin-x64@1.29.2:
    lightningcss-darwin-x64: private
  lightningcss-freebsd-x64@1.29.2:
    lightningcss-freebsd-x64: private
  lightningcss-linux-arm-gnueabihf@1.29.2:
    lightningcss-linux-arm-gnueabihf: private
  lightningcss-linux-arm64-gnu@1.29.2:
    lightningcss-linux-arm64-gnu: private
  lightningcss-linux-arm64-musl@1.29.2:
    lightningcss-linux-arm64-musl: private
  lightningcss-linux-x64-gnu@1.29.2:
    lightningcss-linux-x64-gnu: private
  lightningcss-linux-x64-musl@1.29.2:
    lightningcss-linux-x64-musl: private
  lightningcss-win32-arm64-msvc@1.29.2:
    lightningcss-win32-arm64-msvc: private
  lightningcss-win32-x64-msvc@1.29.2:
    lightningcss-win32-x64-msvc: private
  lightningcss@1.29.2:
    lightningcss: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  listr2@8.3.3:
    listr2: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.memoize@4.1.2:
    lodash.memoize: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.startcase@4.4.0:
    lodash.startcase: private
  lodash@4.17.21:
    lodash: private
  log-symbols@4.1.0:
    log-symbols: private
  log-update@6.1.0:
    log-update: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lop@0.4.2:
    lop: private
  loupe@3.1.3:
    loupe: private
  lowlight@3.3.0:
    lowlight: private
  lru-cache@11.1.0:
    lru-cache: private
  lucide-react@0.511.0(react@18.3.1):
    lucide-react: private
  lz-string@1.5.0:
    lz-string: private
  macos-release@3.3.0:
    macos-release: private
  magic-string@0.30.17:
    magic-string: private
  make-dir@4.0.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  makeerror@1.0.12:
    makeerror: private
  mammoth@1.9.0:
    mammoth: private
  map-or-similar@1.5.0:
    map-or-similar: private
  map-stream@0.1.0:
    map-stream: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  marked@15.0.11:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-definitions@4.0.0:
    mdast-util-definitions: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@2.0.0:
    mdast-util-to-string: private
  mdn-data@2.0.14:
    mdn-data: private
  mdurl@2.0.0:
    mdurl: private
  media-typer@1.1.0:
    media-typer: private
  memoizerific@1.11.3:
    memoizerific: private
  memorystream@0.3.1:
    memorystream: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  mermaid@11.6.0:
    mermaid: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@2.11.4:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mime@1.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-function@5.0.1:
    mimic-function: private
  mimic-response@3.1.0:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@9.0.5:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mitt@3.0.1:
    mitt: private
  mkdirp-classic@0.5.3:
    mkdirp-classic: private
  mlly@1.7.4:
    mlly: private
  mocha@11.2.2:
    mocha: private
  monaco-vscode-textmate-theme-converter@0.1.7(tslib@2.8.1):
    monaco-vscode-textmate-theme-converter: private
  mri@1.2.0:
    mri: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  mz@2.7.0:
    mz: private
  nano-css@5.6.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    nano-css: private
  nanoid@3.3.11:
    nanoid: private
  napi-build-utils@2.0.0:
    napi-build-utils: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@1.0.0:
    negotiator: private
  netmask@2.0.2:
    netmask: private
  nock@14.0.4:
    nock: private
  node-abi@3.75.0:
    node-abi: private
  node-addon-api@4.3.0:
    node-addon-api: private
  node-cache@5.1.2:
    node-cache: private
  node-domexception@1.0.0:
    node-domexception: private
  node-ensure@0.0.0:
    node-ensure: private
  node-fetch@2.7.0:
    node-fetch: private
  node-int64@0.4.0:
    node-int64: private
  node-ipc@12.0.0:
    node-ipc: private
  node-releases@2.0.19:
    node-releases: private
  noms@0.0.0:
    noms: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-normalize-package-bin@4.0.0:
    npm-normalize-package-bin: private
  npm-run-all2@8.0.3:
    npm-run-all2: private
  npm-run-path@6.0.0:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  nwsapi@2.2.20:
    nwsapi: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-treeify@1.1.33:
    object-treeify: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  one-time@0.0.4:
    one-time: private
  onetime@5.1.2:
    onetime: private
  oniguruma-parser@0.12.1:
    oniguruma-parser: private
  oniguruma-to-es@4.3.3:
    oniguruma-to-es: private
  open@10.1.2:
    open: private
  openai@4.103.0(ws@8.18.2)(zod@3.24.4):
    openai: private
  option@0.2.4:
    option: private
  optionator@0.9.4:
    optionator: private
  ora@8.2.0:
    ora: private
  os-name@6.1.0:
    os-name: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  outdent@0.5.0:
    outdent: private
  outvariant@1.4.3:
    outvariant: private
  own-keys@1.0.1:
    own-keys: private
  p-filter@2.1.0:
    p-filter: private
  p-limit@6.2.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-map@2.1.0:
    p-map: private
  p-timeout@6.1.4:
    p-timeout: private
  p-try@2.2.0:
    p-try: private
  p-wait-for@5.0.2:
    p-wait-for: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@0.2.11:
    package-manager-detector: private
  packages/build:
    '@cubent/build': private
  packages/cloud:
    '@cubent/cloud': private
  packages/config-eslint:
    '@cubent/config-eslint': private
  packages/config-typescript:
    '@cubent/config-typescript': private
  packages/telemetry:
    '@cubent/telemetry': private
  packages/types:
    '@cubent/types': private
  pako@1.0.11:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parse-json@5.2.0:
    parse-json: private
  parse-ms@4.0.0:
    parse-ms: private
  parse-semver@1.1.1:
    parse-semver: private
  parse5-htmlparser2-tree-adapter@7.1.0:
    parse5-htmlparser2-tree-adapter: private
  parse5-parser-stream@7.1.2:
    parse5-parser-stream: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-data-parser@0.1.0:
    path-data-parser: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@2.0.0:
    path-scurry: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  pause-stream@0.0.11:
    pause-stream: private
  pdf-parse@1.1.1:
    pdf-parse: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  pidtree@0.6.0:
    pidtree: private
  pify@4.0.1:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkce-challenge@4.1.0:
    pkce-challenge: private
  pkg-dir@4.2.0:
    pkg-dir: private
  pkg-types@2.1.0:
    pkg-types: private
  points-on-curve@0.2.0:
    points-on-curve: private
  points-on-path@0.2.1:
    points-on-path: private
  polished@4.3.1:
    polished: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-load-config@6.0.1(jiti@2.4.2)(postcss@8.5.3)(tsx@4.19.4)(yaml@2.8.0):
    postcss-load-config: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  postcss@8.4.49:
    postcss: private
  posthog-js@1.242.1:
    posthog-js: private
  posthog-node@4.17.2:
    posthog-node: private
  preact@10.26.6:
    preact: private
  prebuild-install@7.1.3:
    prebuild-install: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@6.1.1:
    pretty-bytes: private
  pretty-format@27.5.1:
    pretty-format: private
  pretty-ms@9.2.0:
    pretty-ms: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  progress@2.0.3:
    progress: private
  prompts@2.4.2:
    prompts: private
  prop-types@15.8.1:
    prop-types: private
  propagate@2.0.1:
    propagate: private
  property-information@7.1.0:
    property-information: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-agent@6.5.0:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  ps-tree@1.2.0:
    ps-tree: private
  psl@1.15.0:
    psl: private
  pump@3.0.2:
    pump: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  puppeteer-chromium-resolver@23.0.0:
    puppeteer-chromium-resolver: private
  puppeteer-core@23.11.1:
    puppeteer-core: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.14.0:
    qs: private
  quansync@0.2.10:
    quansync: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  rc@1.2.8:
    rc: private
  react-docgen-typescript@2.2.2(typescript@5.8.3):
    react-docgen-typescript: private
  react-docgen@7.1.1:
    react-docgen: private
  react-dom@18.3.1(react@18.3.1):
    react-dom: private
  react-i18next@15.5.1(i18next@24.2.3(typescript@5.8.3))(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(typescript@5.8.3):
    react-i18next: private
  react-is@17.0.2:
    react-is: private
  react-markdown@9.1.0(@types/react@18.3.21)(react@18.3.1):
    react-markdown: private
  react-refresh@0.17.0:
    react-refresh: private
  react-remark@2.1.0(react@18.3.1):
    react-remark: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.21)(react@18.3.1):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@18.3.21)(react@18.3.1):
    react-remove-scroll: private
  react-style-singleton@2.2.3(@types/react@18.3.21)(react@18.3.1):
    react-style-singleton: private
  react-textarea-autosize@8.5.9(@types/react@18.3.21)(react@18.3.1):
    react-textarea-autosize: private
  react-universal-interface@0.6.2(react@18.3.1)(tslib@2.8.1):
    react-universal-interface: private
  react-use@17.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-use: private
  react-virtuoso@4.12.7(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-virtuoso: private
  react@18.3.1:
    react: private
  read-package-json-fast@4.0.0:
    read-package-json-fast: private
  read-yaml-file@1.1.0:
    read-yaml-file: private
  read@1.0.7:
    read: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@4.1.2:
    readdirp: private
  recast@0.23.11:
    recast: private
  reconnecting-eventsource@1.6.4:
    reconnecting-eventsource: private
  redent@3.0.0:
    redent: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regex-recursion@6.0.2:
    regex-recursion: private
  regex-utilities@2.3.0:
    regex-utilities: private
  regex@6.0.1:
    regex: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  rehype-highlight@7.0.2:
    rehype-highlight: private
  rehype-react@6.2.1:
    rehype-react: private
  remark-gfm@4.0.1:
    remark-gfm: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  remove-markdown@0.6.2:
    remove-markdown: private
  require-directory@2.1.1:
    require-directory: private
  requires-port@1.0.0:
    requires-port: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-cwd@3.0.0:
    resolve-cwd: private
  resolve-from@5.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve.exports@2.0.3:
    resolve.exports: private
  resolve@1.22.10:
    resolve: private
  restore-cursor@5.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  robust-predicates@3.0.2:
    robust-predicates: private
  rollup@4.40.2:
    rollup: private
  roughjs@4.6.6:
    roughjs: private
  router@2.2.0:
    router: private
  rtl-css-js@1.16.1:
    rtl-css-js: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sanitize-filename@1.6.3:
    sanitize-filename: private
  sax@1.4.1:
    sax: private
  saxes@6.0.0:
    saxes: private
  say@0.16.0:
    say: private
  scheduler@0.23.2:
    scheduler: private
  screenfull@5.2.0:
    screenfull: private
  seed-random@2.2.0:
    seed-random: private
  semver@7.7.2:
    semver: private
  send@1.2.0:
    send: private
  serialize-error@11.0.3:
    serialize-error: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-static@2.2.0:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-harmonic-interval@1.0.1:
    set-harmonic-interval: private
  set-proto@1.0.0:
    set-proto: private
  setimmediate@1.0.5:
    setimmediate: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shallow-clone@3.0.1:
    shallow-clone: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shell-quote@1.8.2:
    shell-quote: private
  shiki@3.4.1:
    shiki: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-concat@1.0.1:
    simple-concat: private
  simple-get@4.0.1:
    simple-get: private
  simple-git@3.27.0:
    simple-git: private
  sisteransi@1.0.5:
    sisteransi: private
  slash@3.0.0:
    slash: private
  slice-ansi@5.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  smol-toml@1.3.4:
    smol-toml: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.4:
    socks: private
  sound-play@1.1.0:
    sound-play: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.13:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  spawndamnit@3.0.1:
    spawndamnit: private
  split@0.3.3:
    split: private
  sprintf-js@1.0.3:
    sprintf-js: private
  src:
    cubent: private
  stack-generator@2.0.10:
    stack-generator: private
  stack-utils@2.0.6:
    stack-utils: private
  stackback@0.0.2:
    stackback: private
  stackframe@1.3.4:
    stackframe: private
  stacktrace-gps@3.1.2:
    stacktrace-gps: private
  stacktrace-js@2.0.2:
    stacktrace-js: private
  statuses@2.0.1:
    statuses: private
  std-env@3.9.0:
    std-env: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  storybook-dark-mode@4.0.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)(storybook@8.6.12(prettier@3.5.3)):
    storybook-dark-mode: private
  storybook@8.6.12(prettier@3.5.3):
    storybook: private
  stream-combiner@0.0.4:
    stream-combiner: private
  streamx@2.22.0:
    streamx: private
  strict-event-emitter@0.5.1:
    strict-event-emitter: private
  string-argv@0.3.2:
    string-argv: private
  string-length@4.0.2:
    string-length: private
  string-similarity@4.0.4:
    string-similarity: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.1.1:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  strip-bom@5.0.0:
    strip-bom: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  strip-indent@4.0.0:
    strip-indent: private
  strip-json-comments@5.0.1:
    strip-json-comments: private
  strnum@1.1.2:
    strnum: private
  strong-type@1.1.0:
    strong-type: private
  style-to-js@1.1.16:
    style-to-js: private
  style-to-object@0.3.0:
    style-to-object: private
  styled-components@6.1.18(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    styled-components: private
  stylis@4.3.6:
    stylis: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@9.4.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  symbol-tree@3.2.4:
    symbol-tree: private
  tabbable@5.3.3:
    tabbable: private
  tailwind-merge@2.6.0:
    tailwind-merge: private
  tailwindcss-animate@1.0.7(tailwindcss@4.1.6):
    tailwindcss-animate: private
  tailwindcss@4.1.6:
    tailwindcss: private
  tapable@2.2.1:
    tapable: private
  tar-fs@3.0.9:
    tar-fs: private
  tar-stream@3.1.7:
    tar-stream: private
  tar@7.4.3:
    tar: private
  term-size@2.2.1:
    term-size: private
  test-exclude@6.0.0:
    test-exclude: private
  text-decoder@1.2.3:
    text-decoder: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttle-debounce@3.0.1:
    throttle-debounce: private
  through2@2.0.5:
    through2: private
  through@2.3.8:
    through: private
  tiktoken@1.0.21:
    tiktoken: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.13:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  tmp@0.2.3:
    tmp: private
  tmpl@1.0.5:
    tmpl: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  toidentifier@1.0.1:
    toidentifier: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@3.0.0:
    tr46: private
  tree-kill@1.2.2:
    tree-kill: private
  tree-sitter-wasms@0.1.12:
    tree-sitter-wasms: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  truncate-utf8-bytes@1.0.2:
    truncate-utf8-bytes: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-dedent@2.2.0:
    ts-dedent: private
  ts-easing@0.2.0:
    ts-easing: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  ts-jest@29.3.3(@babel/core@7.27.1)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.27.1))(esbuild@0.25.4)(jest@29.7.0(@types/node@20.17.50)(babel-plugin-macros@3.1.0))(typescript@5.8.3):
    ts-jest: private
  tsconfig-paths@4.2.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsup@8.5.0(jiti@2.4.2)(postcss@8.5.3)(tsx@4.19.4)(typescript@5.8.3)(yaml@2.8.0):
    tsup: private
  tsx@4.19.4:
    tsx: private
  tunnel-agent@0.6.0:
    tunnel-agent: private
  tunnel@0.0.6:
    tunnel: private
  turbo-darwin-64@2.5.4:
    turbo-darwin-64: private
  turbo-darwin-arm64@2.5.4:
    turbo-darwin-arm64: private
  turbo-linux-64@2.5.4:
    turbo-linux-64: private
  turbo-linux-arm64@2.5.4:
    turbo-linux-arm64: private
  turbo-windows-64@2.5.4:
    turbo-windows-64: private
  turbo-windows-arm64@2.5.4:
    turbo-windows-arm64: private
  turndown@7.2.0:
    turndown: private
  type-check@0.4.0:
    type-check: private
  type-detect@4.0.8:
    type-detect: private
  type-fest@2.19.0:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typed-query-selector@2.12.0:
    typed-query-selector: private
  typed-rest-client@1.8.11:
    typed-rest-client: private
  typescript-eslint@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3):
    typescript-eslint: private
  uc.micro@2.1.0:
    uc.micro: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  underscore@1.13.7:
    underscore: private
  undici-types@5.26.5:
    undici-types: private
  undici@5.28.5:
    undici: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  unified@11.0.5:
    unified: private
  unist-builder@2.0.3:
    unist-builder: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-generated@1.1.6:
    unist-util-generated: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-stringify-position@2.0.3:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  unplugin@1.16.1:
    unplugin: private
  untildify@4.0.0:
    untildify: private
  update-browserslist-db@1.1.3(browserslist@4.24.5):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-join@4.0.1:
    url-join: private
  url-parse@1.5.10:
    url-parse: private
  use-callback-ref@1.3.3(@types/react@18.3.21)(react@18.3.1):
    use-callback-ref: private
  use-composed-ref@1.4.0(@types/react@18.3.21)(react@18.3.1):
    use-composed-ref: private
  use-isomorphic-layout-effect@1.2.0(@types/react@18.3.21)(react@18.3.1):
    use-isomorphic-layout-effect: private
  use-latest@1.3.0(@types/react@18.3.21)(react@18.3.1):
    use-latest: private
  use-sidecar@1.1.3(@types/react@18.3.21)(react@18.3.1):
    use-sidecar: private
  use-sound@5.0.0(react@18.3.1):
    use-sound: private
  utf8-byte-length@1.0.5:
    utf8-byte-length: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.12.5:
    util: private
  v8-to-istanbul@9.3.0:
    v8-to-istanbul: private
  vary@1.1.2:
    vary: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  vite-node@3.1.3(@types/node@20.17.50)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0):
    vite-node: private
  vite@6.3.5(@types/node@18.19.100)(jiti@2.4.2)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0):
    vite: private
  vitest@3.1.3(@types/debug@4.1.12)(@types/node@22.15.20)(jiti@2.4.2)(jsdom@20.0.3)(lightningcss@1.29.2)(tsx@4.19.4)(yaml@2.8.0):
    vitest: private
  void-elements@3.1.0:
    void-elements: private
  vscode-jsonrpc@8.2.0:
    vscode-jsonrpc: private
  vscode-languageserver-protocol@3.17.5:
    vscode-languageserver-protocol: private
  vscode-languageserver-textdocument@1.0.12:
    vscode-languageserver-textdocument: private
  vscode-languageserver-types@3.17.5:
    vscode-languageserver-types: private
  vscode-languageserver@9.0.1:
    vscode-languageserver: private
  vscode-material-icons@0.1.1:
    vscode-material-icons: private
  vscode-uri@3.0.8:
    vscode-uri: private
  vscrui@0.2.2(@types/react@18.3.21)(react@18.3.1):
    vscrui: private
  w3c-xmlserializer@4.0.0:
    w3c-xmlserializer: private
  walk-up-path@3.0.1:
    walk-up-path: private
  walker@1.0.8:
    walker: private
  web-namespaces@1.1.4:
    web-namespaces: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  web-tree-sitter@0.22.6:
    web-tree-sitter: private
  web-vitals@4.2.4:
    web-vitals: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  webview-ui:
    '@cubent/vscode-webview': private
  whatwg-encoding@3.1.1:
    whatwg-encoding: private
  whatwg-mimetype@4.0.0:
    whatwg-mimetype: private
  whatwg-url@11.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-pm-runs@1.1.0:
    which-pm-runs: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@4.0.0:
    which: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  wide-align@1.1.5:
    wide-align: private
  windows-release@6.1.0:
    windows-release: private
  word-wrap@1.2.5:
    word-wrap: private
  workerpool@9.2.0:
    workerpool: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@4.0.2:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  xml2js@0.5.0:
    xml2js: private
  xmlbuilder@10.1.1:
    xmlbuilder: private
  xmlchars@2.2.0:
    xmlchars: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs-unparser@2.0.0:
    yargs-unparser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  yazl@2.5.1:
    yazl: private
  yocto-queue@1.2.1:
    yocto-queue: private
  yoctocolors@2.1.1:
    yoctocolors: private
  zod-to-json-schema@3.24.5(zod@3.24.4):
    zod-to-json-schema: private
  zod-to-ts@1.2.0(typescript@5.8.3)(zod@3.24.4):
    zod-to-ts: private
  zod-validation-error@3.4.1(zod@3.24.4):
    zod-validation-error: private
  zod@3.24.4:
    zod: private
  zustand@5.0.5(@types/react@18.3.21)(react@18.3.1):
    zustand: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@vscode/vsce-sign'
  - keytar
  - esbuild
  - puppeteer-chromium-resolver
  - '@tailwindcss/oxide'
  - core-js
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.8.1
pendingBuilds: []
prunedAt: Thu, 26 Jun 2025 01:31:37 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.4'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.40.2'
  - '@rollup/rollup-android-arm64@4.40.2'
  - '@rollup/rollup-darwin-arm64@4.40.2'
  - '@rollup/rollup-darwin-x64@4.40.2'
  - '@rollup/rollup-freebsd-arm64@4.40.2'
  - '@rollup/rollup-freebsd-x64@4.40.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.40.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.40.2'
  - '@rollup/rollup-linux-arm64-gnu@4.40.2'
  - '@rollup/rollup-linux-arm64-musl@4.40.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.40.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.40.2'
  - '@rollup/rollup-linux-riscv64-musl@4.40.2'
  - '@rollup/rollup-linux-s390x-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-gnu@4.40.2'
  - '@rollup/rollup-linux-x64-musl@4.40.2'
  - '@rollup/rollup-win32-arm64-msvc@4.40.2'
  - '@rollup/rollup-win32-ia32-msvc@4.40.2'
  - '@tailwindcss/oxide-android-arm64@4.1.6'
  - '@tailwindcss/oxide-darwin-arm64@4.1.6'
  - '@tailwindcss/oxide-darwin-x64@4.1.6'
  - '@tailwindcss/oxide-freebsd-x64@4.1.6'
  - '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-arm64-musl@4.1.6'
  - '@tailwindcss/oxide-linux-x64-gnu@4.1.6'
  - '@tailwindcss/oxide-linux-x64-musl@4.1.6'
  - '@tailwindcss/oxide-wasm32-wasi@4.1.6'
  - '@tailwindcss/oxide-win32-arm64-msvc@4.1.6'
  - '@vscode/vsce-sign-alpine-arm64@2.0.2'
  - '@vscode/vsce-sign-alpine-x64@2.0.2'
  - '@vscode/vsce-sign-darwin-arm64@2.0.2'
  - '@vscode/vsce-sign-darwin-x64@2.0.2'
  - '@vscode/vsce-sign-linux-arm64@2.0.2'
  - '@vscode/vsce-sign-linux-arm@2.0.2'
  - '@vscode/vsce-sign-linux-x64@2.0.2'
  - '@vscode/vsce-sign-win32-arm64@2.0.2'
  - fsevents@2.3.3
  - lightningcss-darwin-arm64@1.29.2
  - lightningcss-darwin-x64@1.29.2
  - lightningcss-freebsd-x64@1.29.2
  - lightningcss-linux-arm-gnueabihf@1.29.2
  - lightningcss-linux-arm64-gnu@1.29.2
  - lightningcss-linux-arm64-musl@1.29.2
  - lightningcss-linux-x64-gnu@1.29.2
  - lightningcss-linux-x64-musl@1.29.2
  - lightningcss-win32-arm64-msvc@1.29.2
  - turbo-darwin-64@2.5.4
  - turbo-darwin-arm64@2.5.4
  - turbo-linux-64@2.5.4
  - turbo-linux-arm64@2.5.4
  - turbo-windows-arm64@2.5.4
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\node_modules\.pnpm
virtualStoreDirMaxLength: 60
