#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/dist/esm/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/dist/esm/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/dist/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules/rimraf/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/rimraf@6.0.1/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../rimraf/dist/esm/bin.mjs" "$@"
else
  exec node  "$basedir/../rimraf/dist/esm/bin.mjs" "$@"
fi
