{"lastValidatedTimestamp": 1751076966987, "projects": {"C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev": {"name": "cubent"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\apps\\vscode-e2e": {"name": "@qapt-coder/vscode-e2e"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\apps\\vscode-nightly": {"name": "@qapt-coder/vscode-nightly"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\build": {"name": "@cubent/build"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\cloud": {"name": "@cubent/cloud", "version": "0.0.0"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\config-eslint": {"name": "@cubent/config-eslint"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\config-typescript": {"name": "@cubent/config-typescript"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\telemetry": {"name": "@cubent/telemetry", "version": "0.0.0"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\packages\\types": {"name": "@cubent/types", "version": "0.0.0"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\src": {"name": "cubent", "version": "0.22.0"}, "C:\\Users\\<USER>\\Documents\\2 FOLDERS FOR CUBENT\\Cubent.dev\\webview-ui": {"name": "@cubent/vscode-webview"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["src", "webview-ui", "apps/*", "packages/*"]}, "filteredInstall": false}