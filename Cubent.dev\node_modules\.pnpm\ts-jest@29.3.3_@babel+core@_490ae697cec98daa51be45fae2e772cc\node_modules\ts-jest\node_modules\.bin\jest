#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules/jest/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules/jest/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules/jest/bin/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules/jest/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/jest@29.7.0_@types+node@18.19.100_babel-plugin-macros@3.1.0/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../jest/bin/jest.js" "$@"
else
  exec node  "$basedir/../../../jest/bin/jest.js" "$@"
fi
