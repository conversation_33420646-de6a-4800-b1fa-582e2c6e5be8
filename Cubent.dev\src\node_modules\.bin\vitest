#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/vitest@3.1.3_@types+debug@4_857a0198356f2ae3a2a0f2226b3813aa/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/vitest@3.1.3_@types+debug@4_857a0198356f2ae3a2a0f2226b3813aa/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/vitest@3.1.3_@types+debug@4_857a0198356f2ae3a2a0f2226b3813aa/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/vitest@3.1.3_@types+debug@4_857a0198356f2ae3a2a0f2226b3813aa/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
