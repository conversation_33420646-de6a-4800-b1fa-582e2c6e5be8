#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/lib/cjs/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/lib/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/lib/cjs/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/lib/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules/monaco-vscode-textmate-theme-converter/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/monaco-vscode-textmate-theme-converter@0.1.7_tslib@2.8.1/node_modules:/mnt/c/Users/<USER>/Documents/2 FOLDERS FOR CUBENT/Cubent.dev/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../monaco-vscode-textmate-theme-converter/lib/cjs/npx-script.js" "$@"
else
  exec node  "$basedir/../monaco-vscode-textmate-theme-converter/lib/cjs/npx-script.js" "$@"
fi
